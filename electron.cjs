const { app, BrowserWindow, ipcMain } = require('electron');
const path = require('path');
const sqlite3 = require('sqlite3');
const { open } = require('sqlite');
const fs = require('fs');

let mainWindow;
let db;

async function initDatabase() {
  // Store database in the app's user data directory
  const dbPath = path.join(app.getPath('userData'), 'milkentry.db');
  
  db = await open({
    filename: dbPath,
    driver: sqlite3.Database
  });
  
  // Enable foreign keys
  await db.exec('PRAGMA foreign_keys = ON');
  
  // Create tables with relationships
  await db.exec(`
    CREATE TABLE IF NOT EXISTS routes (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      code TEXT UNIQUE,
      route_name TEXT,
      cell_no TEXT,
      ac_code TEXT,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
  `);
  
  await db.exec(`
    CREATE TABLE IF NOT EXISTS suppliers (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      code TEXT UNIQUE,
      route TEXT,
      name TEXT,
      tamil_name TEXT,
      cell_no TEXT,
      bonus_amount REAL,
      milk_rate REAL,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (route) REFERENCES routes(code)
    )
  `);
  
  // Update milk_entries to reference suppliers and routes
  await db.exec(`
    CREATE TABLE IF NOT EXISTS milk_entries (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      date TEXT,
      grn_no TEXT,
      route TEXT,
      shift TEXT,
      name TEXT,
      supplier_code TEXT,
      kgs REAL,
      ltrs REAL,
      fat REAL,
      clr REAL,
      snf REAL,
      ts REAL,
      rate REAL,
      spl_rate REAL,
      amount REAL,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (route) REFERENCES routes(code),
      FOREIGN KEY (supplier_code) REFERENCES suppliers(code)
    )
  `);
  
  await db.exec(`
    CREATE TABLE IF NOT EXISTS transport_incentives (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      route_name TEXT,
      transport_rate REAL,
      incentive_rate REAL,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
  `);
  
  // Add ts_list_types table
  await db.exec(`
    CREATE TABLE IF NOT EXISTS ts_list_types (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      type_id TEXT UNIQUE NOT NULL,
      type_name TEXT NOT NULL,
      is_default INTEGER DEFAULT 0,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
  `);
  
  // Insert default list types if they don't exist
  const defaultTypes = [
    { type_id: 'standard', type_name: 'Standard', is_default: 1 },
    { type_id: 'premium', type_name: 'Premium', is_default: 1 },
    { type_id: 'economy', type_name: 'Economy', is_default: 1 }
  ];
  
  for (const type of defaultTypes) {
    await db.run(
      `INSERT OR IGNORE INTO ts_list_types (type_id, type_name, is_default) VALUES (?, ?, ?)`,
      [type.type_id, type.type_name, type.is_default]
    );
  }
  
  // Update rate_master table to reference ts_list_types
  await db.exec(`
    CREATE TABLE IF NOT EXISTS rate_master (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      route_name TEXT,
      list_type TEXT DEFAULT 'standard',
      from_ts REAL,
      to_ts REAL,
      rate REAL,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (list_type) REFERENCES ts_list_types(type_id)
    )
  `);
  
  // Add customers table
  await db.exec(`
    CREATE TABLE IF NOT EXISTS customers (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      code TEXT,
      customer_id TEXT,
      customer_name TEXT NOT NULL,
      address1 TEXT,
      address2 TEXT,
      place TEXT,
      taluk TEXT,
      pin_code TEXT,
      phone_no TEXT,
      cell_no TEXT,
      aadhaar_number TEXT,
      gstin_no TEXT,
      account_lock INTEGER DEFAULT 0,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
  `);
  
  return db;
}

function createWindow() {
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      preload: path.join(__dirname, 'preload.cjs'),
      nodeIntegration: false,
      contextIsolation: true,
    },
  });

  // In development, load from Vite dev server
  if (process.env.NODE_ENV === 'development') {
    console.log('Loading from development server at http://localhost:5173');
    mainWindow.loadURL('http://localhost:5173');
    // Open DevTools
    mainWindow.webContents.openDevTools();
  } else {
    // In production, load from built files
    const indexPath = path.join(__dirname, 'dist', 'index.html');
    console.log('Loading from production build:', indexPath);
    
    if (fs.existsSync(indexPath)) {
      mainWindow.loadFile(indexPath);
    } else {
      console.error(`Error: Could not find ${indexPath}`);
      mainWindow.loadURL(`data:text/html,<html><body><h1>Error</h1><p>Could not find ${indexPath}</p><p>Please run 'npm run build' first.</p></body></html>`);
      mainWindow.webContents.openDevTools();
    }
  }
}

app.whenReady().then(async () => {
  await initDatabase();
  createWindow();
  
  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) createWindow();
  });
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') app.quit();
});

// IPC handlers for database operations
ipcMain.handle('save-entries', async (event, entries, metadata) => {
  try {
    const stmt = await db.prepare(`
      INSERT INTO milk_entries (
        date, grn_no, route, shift, name, supplier_code, 
        kgs, ltrs, fat, clr, snf, ts, rate, spl_rate, amount
      )
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);
    
    for (const entry of entries) {
      await stmt.run(
        metadata.date,
        metadata.grnNo,
        metadata.route,
        metadata.shift,
        entry.name,
        entry.supplierCode || null,
        entry.kgs,
        entry.ltrs,
        entry.fat,
        entry.clr,
        entry.snf,
        entry.ts,
        entry.rate,
        entry.splRate,
        entry.amount
      );
    }
    
    await stmt.finalize();
    return true;
  } catch (error) {
    console.error('Error saving entries:', error);
    throw error;
  }
});

ipcMain.handle('get-entries', async (event, filters = {}) => {
  try {
    let query = 'SELECT * FROM milk_entries';
    const params = [];
    
    if (Object.keys(filters).length > 0) {
      query += ' WHERE ';
      const conditions = [];
      
      if (filters.date) {
        conditions.push('date = ?');
        params.push(filters.date);
      }
      
      if (filters.shift) {
        conditions.push('shift = ?');
        params.push(filters.shift);
      }
      
      query += conditions.join(' AND ');
    }
    
    query += ' ORDER BY id DESC';
    
    return await db.all(query, params);
  } catch (error) {
    console.error('Error getting entries:', error);
    throw error;
  }
});

// Add these IPC handlers for supplier operations
ipcMain.handle('save-supplier', async (event, supplierData) => {
  try {
    if (supplierData.id) {
      // Update existing supplier
      await db.run(
        `UPDATE suppliers SET 
         code = ?, route = ?, name = ?, tamil_name = ?, 
         cell_no = ?, bonus_amount = ?, milk_rate = ?
         WHERE id = ?`,
        [
          supplierData.code,
          supplierData.route,
          supplierData.name,
          supplierData.tamilName,
          supplierData.cellNo,
          supplierData.bonusAmount,
          supplierData.milkRate,
          supplierData.id
        ]
      );
      return { success: true, id: supplierData.id };
    } else {
      // Insert new supplier
      const result = await db.run(
        `INSERT INTO suppliers 
         (code, route, name, tamil_name, cell_no, bonus_amount, milk_rate)
         VALUES (?, ?, ?, ?, ?, ?, ?)`,
        [
          supplierData.code,
          supplierData.route,
          supplierData.name,
          supplierData.tamilName,
          supplierData.cellNo,
          supplierData.bonusAmount,
          supplierData.milkRate
        ]
      );
      return { success: true, id: result.lastID };
    }
  } catch (error) {
    console.error('Error saving supplier:', error);
    throw error;
  }
});

ipcMain.handle('get-suppliers', async () => {
  try {
    return await db.all('SELECT * FROM suppliers ORDER BY id DESC');
  } catch (error) {
    console.error('Error getting suppliers:', error);
    throw error;
  }
});

ipcMain.handle('delete-supplier', async (event, id) => {
  try {
    await db.run('DELETE FROM suppliers WHERE id = ?', id);
    return { success: true };
  } catch (error) {
    console.error('Error deleting supplier:', error);
    throw error;
  }
});

// Add these IPC handlers for route operations
ipcMain.handle('get-routes', async () => {
  try {
    return await db.all('SELECT * FROM routes ORDER BY code');
  } catch (error) {
    console.error('Error getting routes:', error);
    throw error;
  }
});

ipcMain.handle('save-route', async (event, routeData) => {
  try {
    if (routeData.id) {
      // Update existing route
      await db.run(
        `UPDATE routes SET 
         code = ?, route_name = ?, cell_no = ?, ac_code = ?
         WHERE id = ?`,
        [routeData.code, routeData.routeName, routeData.cellNo, routeData.acCode, routeData.id]
      );
      return { success: true, id: routeData.id };
    } else {
      // Insert new route
      const result = await db.run(
        `INSERT INTO routes (code, route_name, cell_no, ac_code)
         VALUES (?, ?, ?, ?)`,
        [routeData.code, routeData.routeName, routeData.cellNo, routeData.acCode]
      );
      return { success: true, id: result.lastID };
    }
  } catch (error) {
    console.error('Error saving route:', error);
    throw error;
  }
});

ipcMain.handle('delete-route', async (event, routeId) => {
  try {
    await db.run('DELETE FROM routes WHERE id = ?', routeId);
    return { success: true };
  } catch (error) {
    console.error('Error deleting route:', error);
    throw error;
  }
});

// Add these IPC handlers for transport incentive operations
ipcMain.handle('get-transport-incentives', async () => {
  try {
    return await db.all('SELECT * FROM transport_incentives ORDER BY route_name');
  } catch (error) {
    console.error('Error getting transport incentives:', error);
    throw error;
  }
});

ipcMain.handle('save-transport-incentive', async (event, incentiveData) => {
  try {
    if (incentiveData.id) {
      // Update existing incentive
      await db.run(
        `UPDATE transport_incentives SET 
         route_name = ?, transport_rate = ?, incentive_rate = ?
         WHERE id = ?`,
        [
          incentiveData.routeName,
          incentiveData.transportRate,
          incentiveData.incentiveRate,
          incentiveData.id
        ]
      );
      return { success: true, id: incentiveData.id };
    } else {
      // Insert new incentive
      const result = await db.run(
        `INSERT INTO transport_incentives 
         (route_name, transport_rate, incentive_rate)
         VALUES (?, ?, ?)`,
        [
          incentiveData.routeName,
          incentiveData.transportRate,
          incentiveData.incentiveRate
        ]
      );
      return { success: true, id: result.lastID };
    }
  } catch (error) {
    console.error('Error saving transport incentive:', error);
    throw error;
  }
});

ipcMain.handle('delete-transport-incentive', async (event, id) => {
  try {
    await db.run('DELETE FROM transport_incentives WHERE id = ?', id);
    return { success: true };
  } catch (error) {
    console.error('Error deleting transport incentive:', error);
    throw error;
  }
});

// // Add these IPC handlers for rate master operations
// ipcMain.handle('getRates', async (event, routeName = null, listType = 'standard') => {
//   try {
//     let query = 'SELECT * FROM rate_master';
//     const params = [];
    
//     if (routeName || listType) {
//       query += ' WHERE ';
//       const conditions = [];
      
//       if (routeName) {
//         conditions.push('route_name = ?');
//         params.push(routeName);
//       }
      
//       if (listType) {
//         conditions.push('list_type = ?');
//         params.push(listType);
//       }
      
//       query += conditions.join(' AND ');
//     }
    
//     query += ' ORDER BY route_name, from_ts';
    
//     return await db.all(query, params);
//   } catch (error) {
//     console.error('Error getting rates:', error);
//     throw error;
//   }
// });

// ipcMain.handle('saveRate', async (event, rateData) => {
//   try {
//     if (rateData.id) {
//       // Update existing rate
//       await db.run(
//         `UPDATE rate_master SET 
//          route_name = ?, list_type = ?, from_ts = ?, to_ts = ?, rate = ?
//          WHERE id = ?`,
//         [
//           rateData.routeName,
//           rateData.listType || 'standard',
//           rateData.fromTS,
//           rateData.toTS,
//           rateData.rate,
//           rateData.id
//         ]
//       );
//       return { success: true, id: rateData.id };
//     } else {
//       // Insert new rate
//       const result = await db.run(
//         `INSERT INTO rate_master 
//          (route_name, list_type, from_ts, to_ts, rate)
//          VALUES (?, ?, ?, ?, ?)`,
//         [
//           rateData.routeName,
//           rateData.listType || 'standard',
//           rateData.fromTS,
//           rateData.toTS,
//           rateData.rate
//         ]
//       );
//       return { success: true, id: result.lastID };
//     }
//   } catch (error) {
//     console.error('Error saving rate:', error);
//     throw error;
//   }
// });

// ipcMain.handle('deleteRate', async (event, rateId) => {
//   try {
//     await db.run('DELETE FROM rate_master WHERE id = ?', [rateId]);
//     return { success: true };
//   } catch (error) {
//     console.error('Error deleting rate:', error);
//     throw error;
//   }
// });

ipcMain.handle('getRouteNames', async () => {
  try {
    return await db.all('SELECT DISTINCT route_name FROM routes ORDER BY route_name');
  } catch (error) {
    console.error('Error getting route names:', error);
    throw error;
  }
});

// Add these IPC handlers for customer operations
ipcMain.handle('getCustomers', async () => {
  try {
    return await db.all('SELECT * FROM customers ORDER BY customer_name');
  } catch (error) {
    console.error('Error getting customers:', error);
    throw error;
  }
});

ipcMain.handle('saveCustomer', async (event, customerData) => {
  try {
    if (customerData.id) {
      // Update existing customer
      await db.run(
        `UPDATE customers SET 
         code = ?, customer_id = ?, customer_name = ?, address1 = ?,
         address2 = ?, place = ?, taluk = ?, pin_code = ?, phone_no = ?,
         cell_no = ?, aadhaar_number = ?, gstin_no = ?, account_lock = ?
         WHERE id = ?`,
        [
          customerData.code,
          customerData.customerId,
          customerData.customerName,
          customerData.address1,
          customerData.address2,
          customerData.place,
          customerData.taluk,
          customerData.pinCode,
          customerData.phoneNo,
          customerData.cellNo,
          customerData.aadhaarNumber,
          customerData.gstinNo,
          customerData.accountLock ? 1 : 0,
          customerData.id
        ]
      );
      return { success: true, id: customerData.id };
    } else {
      // Insert new customer
      const result = await db.run(
        `INSERT INTO customers 
         (code, customer_id, customer_name, address1, address2, place, taluk,
          pin_code, phone_no, cell_no, aadhaar_number, gstin_no, account_lock)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          customerData.code,
          customerData.customerId,
          customerData.customerName,
          customerData.address1,
          customerData.address2,
          customerData.place,
          customerData.taluk,
          customerData.pinCode,
          customerData.phoneNo,
          customerData.cellNo,
          customerData.aadhaarNumber,
          customerData.gstinNo,
          customerData.accountLock ? 1 : 0
        ]
      );
      return { success: true, id: result.lastID };
    }
  } catch (error) {
    console.error('Error saving customer:', error);
    throw error;
  }
});

ipcMain.handle('deleteCustomer', async (event, id) => {
  try {
    await db.run('DELETE FROM customers WHERE id = ?', id);
    return { success: true };
  } catch (error) {
    console.error('Error deleting customer:', error);
    throw error;
  }
});

// Add a new handler to get suppliers by route
ipcMain.handle('get-suppliers-by-route', async (event, routeCode) => {
  try {
    if (routeCode) {
      return await db.all('SELECT * FROM suppliers WHERE route = ?', [routeCode]);
    } else {
      return await db.all('SELECT * FROM suppliers');
    }
  } catch (error) {
    console.error('Error getting suppliers by route:', error);
    throw error;
  }
});

// Add a generic handler to get data from any table
ipcMain.handle('getTableData', async (event, tableName) => {
  try {
    // Validate table name to prevent SQL injection
    const validTables = [
      'routes', 'suppliers', 'milk_entries', 'transport_incentives', 
      'rate_master', 'weekly_cutoff', 'customers'
    ];
    
    if (!validTables.includes(tableName)) {
      throw new Error('Invalid table name');
    }
    
    return await db.all(`SELECT * FROM ${tableName}`);
  } catch (error) {
    console.error(`Error getting data from ${tableName}:`, error);
    throw error;
  }
});

// Add a handler for report data
ipcMain.handle('getReportData', async (event, filters) => {
  try {
    let query = '';
    const params = [];
    
    if (filters.reportType === 'daily') {
      query = `
        SELECT 
          me.date, me.shift, r.route_name, me.name, 
          me.kgs, me.ltrs, me.fat, me.snf, me.rate, me.amount
        FROM milk_entries me
        LEFT JOIN routes r ON me.route = r.code
        WHERE me.date BETWEEN ? AND ?
      `;
      params.push(filters.startDate, filters.endDate);
      
      if (filters.shift) {
        query += ' AND me.shift = ?';
        params.push(filters.shift);
      }
      
      if (filters.route) {
        query += ' AND me.route = ?';
        params.push(filters.route);
      }
      
      query += ' ORDER BY me.date, me.shift, r.route_name, me.name';
    } 
    else if (filters.reportType === 'supplier') {
      query = `
        SELECT 
          me.name,
          SUM(me.kgs) as total_kgs,
          SUM(me.ltrs) as total_ltrs,
          AVG(me.fat) as avg_fat,
          AVG(me.snf) as avg_snf,
          SUM(me.amount) as total_amount
        FROM milk_entries me
        WHERE me.date BETWEEN ? AND ?
      `;
      params.push(filters.startDate, filters.endDate);
      
      if (filters.shift) {
        query += ' AND me.shift = ?';
        params.push(filters.shift);
      }
      
      if (filters.route) {
        query += ' AND me.route = ?';
        params.push(filters.route);
      }
      
      query += ' GROUP BY me.name ORDER BY me.name';
    }
    else if (filters.reportType === 'route') {
      query = `
        SELECT 
          r.route_name,
          SUM(me.kgs) as total_kgs,
          SUM(me.ltrs) as total_ltrs,
          AVG(me.fat) as avg_fat,
          AVG(me.snf) as avg_snf,
          SUM(me.amount) as total_amount
        FROM milk_entries me
        LEFT JOIN routes r ON me.route = r.code
        WHERE me.date BETWEEN ? AND ?
      `;
      params.push(filters.startDate, filters.endDate);
      
      if (filters.shift) {
        query += ' AND me.shift = ?';
        params.push(filters.shift);
      }
      
      if (filters.route) {
        query += ' AND me.route = ?';
        params.push(filters.route);
      }
      
      query += ' GROUP BY me.route, r.route_name ORDER BY r.route_name';
    }
    
    return await db.all(query, params);
  } catch (error) {
    console.error('Error getting report data:', error);
    throw error;
  }
});

// Add a handler for importing table data
ipcMain.handle('importTableData', async (event, tableName, data) => {
  try {
    // Validate table name to prevent SQL injection
    const validTables = [
      'routes', 'suppliers', 'milk_entries', 'transport_incentives', 
      'rate_master', 'weekly_cutoff', 'customers'
    ];
    
    if (!validTables.includes(tableName)) {
      throw new Error('Invalid table name');
    }
    
    // Begin transaction
    await db.run('BEGIN TRANSACTION');
    
    let successCount = 0;
    
    // Process each row
    for (const row of data) {
      // Convert object keys to snake_case for database
      const dbRow = {};
      Object.keys(row).forEach(key => {
        // Convert camelCase or spaces to snake_case
        const dbKey = key
          .replace(/([A-Z])/g, '_$1')
          .replace(/\s+/g, '_')
          .toLowerCase()
          .replace(/^_/, '');
        dbRow[dbKey] = row[key];
      });
      
      // Get column names and values
      const columns = Object.keys(dbRow);
      const values = Object.values(dbRow);
      const placeholders = columns.map(() => '?').join(', ');
      
      // Create SQL statement
      const sql = `INSERT OR REPLACE INTO ${tableName} (${columns.join(', ')}) VALUES (${placeholders})`;
      
      // Execute statement
      await db.run(sql, values);
      successCount++;
    }
    
    // Commit transaction
    await db.run('COMMIT');
    
    return { success: true, count: successCount };
  } catch (error) {
    // Rollback on error
    await db.run('ROLLBACK');
    console.error(`Error importing data to ${tableName}:`, error);
    throw error;
  }
});

// Add handler to get rate for a specific TS value
ipcMain.handle('getRateForTS', async (event, ts, routeName, listType = 'standard') => {
  try {
    if (!ts || !routeName) {
      return null;
    }
    
    const tsValue = parseFloat(ts);
    
    // Find the rate where the TS falls between from_ts and to_ts
    const query = `
      SELECT * FROM rate_master 
      WHERE route_name = ? 
      AND list_type = ? 
      AND ? >= from_ts 
      AND ? <= to_ts
    `;
    
    const rate = await db.get(query, [routeName, listType, tsValue, tsValue]);
    return rate || null;
  } catch (error) {
    console.error('Error getting rate for TS:', error);
    throw error;
  }
});

// Add handler to get rates for a specific route and list type
ipcMain.handle('getRates', async (event, routeName = null, listType = 'standard') => {
  try {
    let query = 'SELECT * FROM rate_master';
    const params = [];
    
    if (routeName || listType) {
      query += ' WHERE ';
      const conditions = [];
      
      if (routeName) {
        conditions.push('route_name = ?');
        params.push(routeName);
      }
      
      if (listType) {
        conditions.push('list_type = ?');
        params.push(listType);
      }
      
      query += conditions.join(' AND ');
    }
    
    query += ' ORDER BY route_name, from_ts';
    
    return await db.all(query, params);
  } catch (error) {
    console.error('Error getting rates:', error);
    throw error;
  }
});

// Add handler to save or update a rate
ipcMain.handle('saveRate', async (event, rateData) => {
  try {
    if (rateData.id) {
      // Update existing rate
      await db.run(
        `UPDATE rate_master SET 
         route_name = ?, list_type = ?, from_ts = ?, to_ts = ?, rate = ?
         WHERE id = ?`,
        [rateData.routeName, rateData.listType, rateData.fromTS, rateData.toTS, rateData.rate, rateData.id]
      );
      return { success: true, id: rateData.id };
    } else {
      // Insert new rate
      const result = await db.run(
        `INSERT INTO rate_master (route_name, list_type, from_ts, to_ts, rate)
         VALUES (?, ?, ?, ?, ?)`,
        [rateData.routeName, rateData.listType, rateData.fromTS, rateData.toTS, rateData.rate]
      );
      return { success: true, id: result.lastID };
    }
  } catch (error) {
    console.error('Error saving rate:', error);
    throw error;
  }
});

// Add handler to delete a rate
ipcMain.handle('deleteRate', async (event, id) => {
  try {
    await db.run('DELETE FROM rate_master WHERE id = ?', id);
    return { success: true };
  } catch (error) {
    console.error('Error deleting rate:', error);
    throw error;
  }
});

// Add these IPC handlers for TS list types operations
ipcMain.handle('get-ts-list-types', async () => {
  try {
    return await db.all('SELECT * FROM ts_list_types ORDER BY type_name');
  } catch (error) {
    console.error('Error getting TS list types:', error);
    throw error;
  }
});

ipcMain.handle('save-ts-list-type', async (event, listTypeData) => {
  try {
    if (listTypeData.id) {
      // Update existing list type
      await db.run(
        `UPDATE ts_list_types SET 
         type_id = ?, type_name = ?
         WHERE id = ?`,
        [listTypeData.typeId, listTypeData.typeName, listTypeData.id]
      );
      return { success: true, id: listTypeData.id };
    } else {
      // Insert new list type
      const result = await db.run(
        `INSERT INTO ts_list_types (type_id, type_name, is_default)
         VALUES (?, ?, 0)`,
        [listTypeData.typeId, listTypeData.typeName]
      );
      return { success: true, id: result.lastID };
    }
  } catch (error) {
    console.error('Error saving TS list type:', error);
    throw error;
  }
});

ipcMain.handle('delete-ts-list-type', async (event, id) => {
  try {
    // Check if this is a default list type
    const listType = await db.get('SELECT is_default FROM ts_list_types WHERE id = ?', id);
    
    if (listType && listType.is_default === 1) {
      throw new Error('Cannot delete default list types');
    }
    
    // Check if this list type is being used in rate_master
    const usageCount = await db.get(
      'SELECT COUNT(*) as count FROM rate_master WHERE list_type = (SELECT type_id FROM ts_list_types WHERE id = ?)',
      id
    );
    
    if (usageCount && usageCount.count > 0) {
      throw new Error('Cannot delete list type that is being used in rate master');
    }
    
    await db.run('DELETE FROM ts_list_types WHERE id = ?', id);
    return { success: true };
  } catch (error) {
    console.error('Error deleting TS list type:', error);
    throw error;
  }
});
