{"name": "milk-entry-app", "private": true, "version": "0.0.0", "type": "module", "main": "electron.cjs", "scripts": {"dev": "vite", "start": "cross-env NODE_ENV=production electron .", "electron-dev": "concurrently \"cross-env BROWSER=none npm run dev\" \"wait-on http://localhost:5173 && cross-env NODE_ENV=development electron .\"", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "electron:build": "vite build && electron-builder", "electron:start": "npm run build && cross-env NODE_ENV=production electron ."}, "dependencies": {"@tailwindcss/vite": "^4.1.7", "react": "^19.1.0", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "react-router-dom": "^7.6.0", "sqlite": "^5.1.1", "sqlite3": "^5.1.7", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/js": "^9.25.0", "@tailwindcss/postcss": "^4.1.7", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "concurrently": "^8.2.2", "cross-env": "^7.0.3", "electron": "^36.2.1", "electron-builder": "^24.13.3", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.5.3", "tailwindcss": "^4.1.7", "vite": "^6.3.5", "wait-on": "^7.2.0"}}