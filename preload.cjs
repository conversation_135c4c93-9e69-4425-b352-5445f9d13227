const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  saveEntries: (entries, metadata) => ipcRenderer.invoke('save-entries', entries, metadata),
  getEntries: (filters) => ipcRenderer.invoke('get-entries', filters),
  saveSupplier: (supplierData) => ipcRenderer.invoke('save-supplier', supplierData),
  getSuppliers: () => ipcRenderer.invoke('get-suppliers'),
  deleteSupplier: (id) => ipcRenderer.invoke('delete-supplier', id),
  getRoutes: () => ipcRenderer.invoke('get-routes'),
  saveRoute: (routeData) => ipcRenderer.invoke('save-route', routeData),
  deleteRoute: (routeId) => ipcRenderer.invoke('delete-route', routeId),
  getTransportIncentives: () => ipcRenderer.invoke('get-transport-incentives'),
  saveTransportIncentive: (data) => ipcRenderer.invoke('save-transport-incentive', data),
  deleteTransportIncentive: (id) => ipcRenderer.invoke('delete-transport-incentive', id),
  getTSListTypes: () => ipcRenderer.invoke('get-ts-list-types'),
  saveTSListType: (listTypeData) => ipcRenderer.invoke('save-ts-list-type', listTypeData),
  deleteTSListType: (id) => ipcRenderer.invoke('delete-ts-list-type', id),
  getRates: (routeName, listType) => ipcRenderer.invoke('getRates', routeName, listType),
  saveRate: (rateData) => ipcRenderer.invoke('saveRate', rateData),
  deleteRate: (rateId) => ipcRenderer.invoke('deleteRate', rateId),
  getRateForTS: (ts, routeName, listType) => ipcRenderer.invoke('getRateForTS', ts, routeName, listType),
  getSuppliersByRoute: (routeCode) => ipcRenderer.invoke('get-suppliers-by-route', routeCode),
  getTableData: (tableName) => ipcRenderer.invoke('getTableData', tableName),
  getReportData: (filters) => ipcRenderer.invoke('getReportData', filters),
  importTableData: (tableName, data) => ipcRenderer.invoke('importTableData', tableName, data),
});
