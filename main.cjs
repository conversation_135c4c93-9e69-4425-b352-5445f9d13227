import { app, BrowserWindow, ipcMain } from 'electron';
import path from 'path';
import sqlite3 from 'sqlite3';
import { open } from 'sqlite';

let mainWindow;
let db;

async function initDatabase() {
  // Store database in the app's user data directory
  const dbPath = path.join(app.getPath('userData'), 'milkentry.db');
  
  db = await open({
    filename: dbPath,
    driver: sqlite3.Database
  });
  
  // Create tables if they don't exist
  await db.exec(`
    CREATE TABLE IF NOT EXISTS milk_entries (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      date TEXT,
      grn_no TEXT,
      route TEXT,
      shift TEXT,
      name TEXT,
      supplier_code TEXT,
      kgs REAL,
      ltrs REAL,
      fat REAL,
      clr REAL,
      snf REAL,
      ts REAL,
      rate REAL,
      spl_rate REAL,
      amount REAL,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
  `);
  
  // Create rate_master table if it doesn't exist
  await db.exec(`
    CREATE TABLE IF NOT EXISTS rate_master (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      route_name TEXT,
      list_type TEXT DEFAULT 'standard',
      from_ts REAL,
      to_ts REAL,
      rate REAL,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
  `);
  
  return db;
}


function createWindow() {
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      preload: path.join(__dirname, 'preload.js'),
      nodeIntegration: false,
      contextIsolation: true,
    },
  });

  // In development, load from Vite dev server
  if (process.env.NODE_ENV === 'development') {
    mainWindow.loadURL('http://localhost:5173');
    // Open DevTools
    mainWindow.webContents.openDevTools();
  } else {
    // In production, load from built files
    mainWindow.loadFile(path.join(__dirname, 'dist', 'index.html'));
  }
}

app.whenReady().then(async () => {
  await initDatabase();
  createWindow();
  
  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) createWindow();
  });
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') app.quit();
});

// IPC handlers for database operations
ipcMain.handle('save-entries', async (event, entries, metadata) => {
  try {
    const stmt = await db.prepare(`
      INSERT INTO milk_entries (date, grn_no, route, shift, name, kgs, ltrs, fat, clr, snf, ts, rate, spl_rate, amount)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);
    
    for (const entry of entries) {
      await stmt.run(
        metadata.date,
        metadata.grnNo,
        metadata.route,
        metadata.shift,
        entry.name,
        entry.kgs,
        entry.ltrs,
        entry.fat,
        entry.clr,
        entry.snf,
        entry.ts,
        entry.rate,
        entry.splRate,
        entry.amount
      );
    }
    
    await stmt.finalize();
    return true;
  } catch (error) {
    console.error('Error saving entries:', error);
    throw error;
  }
});

ipcMain.handle('get-entries', async (event, filters = {}) => {
  try {
    let query = 'SELECT * FROM milk_entries';
    const params = [];
    
    if (Object.keys(filters).length > 0) {
      query += ' WHERE ';
      const conditions = [];
      
      if (filters.date) {
        conditions.push('date = ?');
        params.push(filters.date);
      }
      
      if (filters.shift) {
        conditions.push('shift = ?');
        params.push(filters.shift);
      }
      
      query += conditions.join(' AND ');
    }
    
    query += ' ORDER BY id DESC';
    
    return await db.all(query, params);
  } catch (error) {
    console.error('Error getting entries:', error);
    throw error;
  }
});
