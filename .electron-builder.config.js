/**
 * @type {import('electron-builder').Configuration}
 * @see https://www.electron.build/configuration/configuration
 */
module.exports = {
  appId: "com.milk-entry-app",
  productName: "Milk Entry App",
  directories: {
    output: "release",
    buildResources: "resources",
  },
  files: [
    "dist/**/*",
    "electron.cjs",
    "preload.cjs",
    "package.json"
  ],
  win: {
    target: [
      {
        target: "nsis",
        arch: ["x64"]
      }
    ]
  },
  mac: {
    target: ["dmg"]
  },
  linux: {
    target: ["AppImage"]
  }
};
