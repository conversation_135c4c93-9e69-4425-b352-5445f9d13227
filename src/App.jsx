
import React from 'react';
import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom';
import Layout from './components/Layout';
import MilkEntry from './pages/MilkEntry';
import SuppliersName from './pages/SuppliersName';
import RouteName from './pages/RouteName';
import TransportInsentive from './pages/TransportInsentive';
import RateMaster from './pages/RateMaster';
import WeeklyCutoff from './pages/WeeklyCutoff';
import SupplierWiseRate from './pages/SupplierWiseRate';
import CustomerMaster from './pages/CustomerMaster';
import Reports from './pages/Reports';
import Tools from './pages/Tools';

function App() {
  return (
    <BrowserRouter>
      <Routes>
        <Route path="/" element={<Layout />}>
          <Route index element={<Navigate to="/entry" replace />} />
          <Route path="entry" element={<MilkEntry />} />
          <Route path="masters/suppliers-name" element={<SuppliersName />} />
          <Route path="masters/route-name" element={<RouteName />} />
          <Route path="masters/transport-insentive" element={<TransportInsentive />} />
          <Route path="masters/rate-master" element={<RateMaster />} />
          <Route path="masters/weekly-cutoff" element={<WeeklyCutoff />} />
          <Route path="masters/supplierwise-rate" element={<SupplierWiseRate />} />
          <Route path="masters/customer-master" element={<CustomerMaster />} />
          <Route path="reports" element={<Reports />} />
          <Route path="tools" element={<Tools />} />
        </Route>
      </Routes>
    </BrowserRouter>
  );
}

export default App;
