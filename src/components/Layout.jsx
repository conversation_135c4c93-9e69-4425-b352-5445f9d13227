import React, { useState } from 'react';
import { Link, Outlet, useLocation } from 'react-router-dom';

function Layout() {
  const [showMastersDropdown, setShowMastersDropdown] = useState(false);
  const location = useLocation();

  return (
    <div className="bg-gray-50 min-h-screen">
      <div className="bg-blue-300 text-white p-3 text-2xl font-bold rounded-t-md shadow-md">
        Aadhan Milk Collection Software
      </div>
      
      <div className="flex bg-blue-300 text-white shadow-md relative">
        <div 
          className="px-4 py-2 hover:bg-blue-800 cursor-pointer transition-colors"
          onClick={() => setShowMastersDropdown(!showMastersDropdown)}
        >
          Masters
        </div>
        {showMastersDropdown && (
          <div className="absolute top-full left-0 bg-blue-600 text-white shadow-md z-10 w-64">
            <Link to="/masters/suppliers-name" className="block px-4 py-2 hover:bg-blue-800">Suppliers Name</Link>
            <Link to="/masters/route-name" className="block px-4 py-2 hover:bg-blue-800">Route Name</Link>
            <Link to="/masters/transport-insentive" className="block px-4 py-2 hover:bg-blue-800">Transport-Insentive</Link>
            <Link to="/masters/rate-master" className={`block px-4 py-2 ${location.pathname === '/masters/rate-master' ? 'bg-purple-600 hover:bg-purple-700' : 'hover:bg-blue-800'}`}>Rate Master</Link>
            <Link to="/masters/weekly-cutoff" className="block px-4 py-2 hover:bg-blue-800">Weekly Cutoff Date</Link>
            <Link to="/masters/supplierwise-rate" className="block px-4 py-2 hover:bg-blue-800">Supplierwise Rate</Link>
            <Link to="/masters/customer-master" className="block px-4 py-2 hover:bg-blue-800">Customer Master</Link>
          </div>
        )}
        <Link to="/entry" className={`px-4 py-2 ${location.pathname === '/entry' ? 'bg-teal-600' : 'hover:bg-blue-800'} cursor-pointer transition-colors`}>Entry</Link>
        <Link to="/reports" className="px-4 py-2 hover:bg-blue-800 cursor-pointer transition-colors">Reports</Link>
        <Link to="/tools" className="px-4 py-2 hover:bg-blue-800 cursor-pointer transition-colors">Tools</Link>
      </div>
    
      
      <div className="border border-gray-200 bg-white rounded-b-md shadow-md">
        <Outlet />
      </div>
    </div>
  );
}

export default Layout;