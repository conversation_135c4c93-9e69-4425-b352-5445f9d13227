import React, { useState, useRef, useEffect } from 'react';
import * as XLSX from 'xlsx';

function MilkEntry() {
  const [entries, setEntries] = useState([]);
  const [form, setForm] = useState({
    name: '', kgs: '', ltrs: '', fat: '', snf: '', ts: '', rate: '', splRate: '', amount: '', supplierCode: ''
  });
  const [date, setDate] = useState(new Date().toLocaleDateString('en-GB'));
  const [grnNo, setGrnNo] = useState('');
  const [route, setRoute] = useState('');
  const [shift, setShift] = useState('Morning');
  const [routes, setRoutes] = useState([]);
  const [suppliers, setSuppliers] = useState([]);
  const [filteredSuppliers, setFilteredSuppliers] = useState([]);
  
  // Add state for tracking edit mode
  const [isEditing, setIsEditing] = useState(false);
  const [editingIndex, setEditingIndex] = useState(null);
  
  // Add state for TS list types
  const [listTypes, setListTypes] = useState([]);
  const [selectedListType, setSelectedListType] = useState('standard');

  // Load TS list types from database
  useEffect(() => {
    loadTSListTypes();
  }, []);

  const loadTSListTypes = async () => {
    if (window.electronAPI) {
      try {
        const data = await window.electronAPI.getTSListTypes();
        if (data && data.length > 0) {
          setListTypes(data);
          // Set default list type if available
          const defaultType = data.find(type => type.is_default === 1);
          if (defaultType) {
            setSelectedListType(defaultType.type_id);
          }
        }
      } catch (error) {
        console.error('Failed to load TS list types:', error);
      }
    }
  };

  // Load routes, suppliers and entries
  useEffect(() => {
    loadRoutes();
    loadSuppliers();
    loadEntries();
  }, [date, shift]);
  
  // Filter suppliers when route changes
  useEffect(() => {
    if (route) {
      setFilteredSuppliers(suppliers.filter(supplier => supplier.route === route));
    } else {
      setFilteredSuppliers(suppliers);
    }
  }, [route, suppliers]);
  
  const loadRoutes = async () => {
    if (window.electronAPI) {
      try {
        const data = await window.electronAPI.getRoutes();
        setRoutes(data || []);
      } catch (error) {
        console.error('Failed to load routes:', error);
      }
    }
  };
  
  const loadSuppliers = async () => {
    if (window.electronAPI) {
      try {
        const data = await window.electronAPI.getSuppliers();
        // Make sure we have all the fields we need
        const formattedSuppliers = data.map(supplier => ({
          ...supplier,
          name: supplier.name || '',
          code: supplier.code || '',
          route: supplier.route || '',
          milk_rate: supplier.milk_rate || ''
        }));
        setSuppliers(formattedSuppliers || []);
      } catch (error) {
        console.error('Failed to load suppliers:', error);
      }
    }
  };
  
  const loadEntries = async () => {
    if (window.electronAPI) {
      try {
        const loadedEntries = await window.electronAPI.getEntries({ date, shift });
        if (loadedEntries && loadedEntries.length > 0) {
          // Format entries to match component state format
          const formattedEntries = loadedEntries.map(entry => ({
            name: entry.name,
            kgs: entry.kgs,
            ltrs: entry.ltrs,
            fat: entry.fat,
            snf: entry.snf,
            ts: entry.ts,
            rate: entry.rate,
            splRate: entry.spl_rate,
            amount: entry.amount
          }));
          setEntries(formattedEntries);
        }
      } catch (error) {
        console.error('Failed to load entries:', error);
      }
    }
  };
  
  const saveEntriesToDatabase = async () => {
    if (entries.length === 0) {
      alert("No entries to save");
      return;
    }
    
    if (!route) {
      alert("Please select a route");
      return;
    }
    
    if (window.electronAPI) {
      try {
        const metadata = { date, grnNo, route, shift };
        // Make sure each entry has a supplier code
        const entriesWithCodes = entries.map(entry => {
          // If entry doesn't have a supplier code, try to find it
          if (!entry.supplierCode) {
            const matchedSupplier = suppliers.find(s => 
              s.name.toLowerCase() === entry.name.toLowerCase() && s.route === route
            );
            if (matchedSupplier) {
              return { ...entry, supplierCode: matchedSupplier.code };
            }
          }
          return entry;
        });
        
        await window.electronAPI.saveEntries(entriesWithCodes, metadata);
        alert("Entries saved successfully");
      } catch (error) {
        console.error('Failed to save entries:', error);
        alert("Failed to save entries: " + error.message);
      }
    }
  };

  // Create refs for each input field
  const inputRefs = {
    name: useRef(null),
    kgs: useRef(null),
    ltrs: useRef(null),
    fat: useRef(null),
    snf: useRef(null),
    ts: useRef(null),
    rate: useRef(null),
    splRate: useRef(null),
    amount: useRef(null)
  };
  
  // Define the order of fields for navigation
  const fieldOrder = ['name', 'kgs', 'ltrs', 'fat', 'snf', 'ts', 'rate', 'splRate', 'amount'];

  // Define which fields are auto-calculated/read-only
  const autoCalculatedFields = ['ltrs', 'ts', 'rate', 'amount'];

  // Add this function to fetch rate based on TS with debugging
  const fetchRateBasedOnTS = async (ts, routeCode, listType = 'standard') => {
    if (!ts || !routeCode || !window.electronAPI) {
      return null;
    }
    
    try {
      const rate = await window.electronAPI.getRateForTS(ts, routeCode, listType);
      return rate;
    } catch (error) {
      console.error('Failed to fetch rate:', error);
      return null;
    }
  };

  // Add a useEffect to update rate when route changes
  useEffect(() => {
    if (route && form.ts && parseFloat(form.ts) > 0) {
      console.log('Route changed, updating rate based on TS:', form.ts);
      fetchRateBasedOnTS(form.ts, route, selectedListType).then(rate => {
        if (rate) {
          setForm(prevForm => ({
            ...prevForm,
            rate: rate.toFixed(2),
            amount: ((+rate || 0) * (+prevForm.ltrs || 0)).toFixed(2)
          }));
        }
      });
    }
  }, [route, selectedListType]);

  const handleChange = async (e) => {
    const { name, value } = e.target;
    const updatedForm = { ...form, [name]: value };
    
    // Auto-calculate ltrs when kgs changes (using factor of 1.3)
    if (name === 'kgs' && value) {
      const calculatedLtrs = ((+value || 0) * 1.3).toFixed(2);
      updatedForm.ltrs = calculatedLtrs;
      
      // Also update amount if rate exists
      if (updatedForm.rate) {
        updatedForm.amount = ((+updatedForm.rate || 0) * (+calculatedLtrs || 0)).toFixed(2);
      }
    }
    
    // Auto-calculate TS when fat or snf changes (TS = FAT + SNF)
    if (name === 'fat' || name === 'snf') {
      const calculatedTS = ((+updatedForm.fat || 0) + (+updatedForm.snf || 0)).toFixed(2);
      updatedForm.ts = calculatedTS;
      
      console.log('Calculated TS:', calculatedTS, 'Route:', route);
      
      // Auto-fetch rate based on TS and route
      if (route && calculatedTS && parseFloat(calculatedTS) > 0) {
        try {
          const rate = await fetchRateBasedOnTS(calculatedTS, route, selectedListType);
          console.log('Fetched rate:', rate);
          
          if (rate) {
            updatedForm.rate = rate.toFixed(2);
            // Also update amount since rate changed
            updatedForm.amount = ((+rate || 0) * (+updatedForm.ltrs || 0)).toFixed(2);
          } else {
            console.log('No rate found for TS:', calculatedTS);
          }
        } catch (error) {
          console.error('Error fetching rate:', error);
        }
      }
    }
    
    // Auto-calculate amount when rate or ltrs changes
    if (name === 'rate' || name === 'ltrs') {
      updatedForm.amount = ((+updatedForm.rate || 0) * (+updatedForm.ltrs || 0)).toFixed(2);
    }
    
    setForm(updatedForm);
  };

  const handleKeyDown = (e, fieldName) => {
    // Function to find the next editable field
    const findNextEditableField = (currentIndex, direction) => {
      let nextIndex = currentIndex;
      let nextField;
      
      do {
        nextIndex = (nextIndex + direction + fieldOrder.length) % fieldOrder.length;
        nextField = fieldOrder[nextIndex];
        // Keep looking until we find an editable field or we've checked all fields
        if (!autoCalculatedFields.includes(nextField) || nextIndex === currentIndex) {
          break;
        }
      } while (autoCalculatedFields.includes(nextField));
      
      return nextField;
    };

    // Handle arrow key navigation
    if (e.key === 'ArrowRight' || e.key === 'Tab') {
      e.preventDefault();
      const currentIndex = fieldOrder.indexOf(fieldName);
      const nextField = findNextEditableField(currentIndex, 1); // Move forward
      inputRefs[nextField].current.focus();
    } else if (e.key === 'ArrowLeft' || (e.key === 'Tab' && e.shiftKey)) {
      e.preventDefault();
      const currentIndex = fieldOrder.indexOf(fieldName);
      const prevField = findNextEditableField(currentIndex, -1); // Move backward
      inputRefs[prevField].current.focus();
    } else if (e.key === 'Enter') {
      e.preventDefault();
      // If Enter is pressed on the last field, add the entry
      if (fieldName === 'amount' || fieldName === 'splRate') {
        handleAdd();
        // Focus back on the first field after adding
        setTimeout(() => {
          inputRefs.name.current.focus();
        }, 0);
      } else {
        // Otherwise move to the next field
        const currentIndex = fieldOrder.indexOf(fieldName);
        const nextField = findNextEditableField(currentIndex, 1); // Move forward
        inputRefs[nextField].current.focus();
      }
    }
  };

  const handleAdd = () => {
    if (!form.name) {
      alert("Please enter supplier's name");
      return;
    }
    
    // Find the selected supplier to get the code if not already set
    if (!form.supplierCode && route) {
      const matchedSupplier = suppliers.find(s => 
        s.name.toLowerCase() === form.name.toLowerCase() && s.route === route
      );
      if (matchedSupplier) {
        form.supplierCode = matchedSupplier.code;
      }
    }
    
    // Ensure amount is calculated
    const amount = ((+form.rate || 0) * (+form.ltrs || 0)).toFixed(2);
    const entryWithAmount = { 
      ...form, 
      amount,
      // Make sure to include the supplier code
      supplierCode: form.supplierCode || ''
    };
    
    if (isEditing && editingIndex !== null) {
      // Update existing entry
      const updatedEntries = [...entries];
      updatedEntries[editingIndex] = entryWithAmount;
      setEntries(updatedEntries);
      setIsEditing(false);
      setEditingIndex(null);
    } else {
      // Add new entry
      setEntries([...entries, entryWithAmount]);
    }
    
    // Reset form but keep the route selected
    setForm({ 
      name: '', 
      kgs: '', 
      ltrs: '', 
      fat: '', 
      snf: '', 
      ts: '', 
      rate: '', 
      splRate: '', 
      amount: '', 
      supplierCode: '' 
    });
    
    // Focus back on the name field
    setTimeout(() => {
      inputRefs.name.current.focus();
    }, 0);
  };

  const exportToExcel = () => {
    if (entries.length === 0) {
      alert("No data to export");
      return;
    }

    // Create a workbook
    const wb = XLSX.utils.book_new();
    
    // Create header rows with metadata
    const headerData = [
      ["MILK PURCHASE ENTRY REPORT"],
      ["Date:", date, "", "GRN No:", grnNo],
      ["Route:", route, "", "Shift:", shift],
      [""],  // Empty row for spacing
      ["Name", "KGs", "LTRs", "FAT", "SNF", "TS", "Rate", "Spl Rate", "Amount"]
    ];
    
    // Convert entries to rows
    const entriesData = entries.map(entry => [
      entry.name,
      Number(entry.kgs) || 0,
      Number(entry.ltrs) || 0,
      Number(entry.fat) || 0,
      Number(entry.snf) || 0,
      Number(entry.ts) || 0,
      Number(entry.rate) || 0,
      Number(entry.splRate) || 0,
      Number(entry.amount) || 0
    ]);
    
    // Add totals row
    const totalKgs = entries.reduce((sum, entry) => sum + (+entry.kgs || 0), 0);
    const totalLtrs = entries.reduce((sum, entry) => sum + (+entry.ltrs || 0), 0);
    const totalAmount = entries.reduce((sum, entry) => sum + (+entry.amount || 0), 0);
    
    const totalsRow = [
      "Total", 
      totalKgs, 
      totalLtrs, 
      "", "", "", "", "", "", 
      totalAmount
    ];
    
    // Combine all rows
    const allData = [...headerData, ...entriesData, totalsRow];
    
    // Create worksheet
    const ws = XLSX.utils.aoa_to_sheet(allData);
    
    // Set column widths
    const colWidths = [
      { wch: 20 }, // Name
      { wch: 10 }, // KGs
      { wch: 10 }, // LTRs
      { wch: 10 }, // FAT
      { wch: 10 }, // SNF
      { wch: 10 }, // TS
      { wch: 10 }, // Rate
      { wch: 10 }, // Spl Rate
      { wch: 12 }  // Amount
    ];
    ws['!cols'] = colWidths;
    
    // Add worksheet to workbook
    XLSX.utils.book_append_sheet(wb, ws, "Milk Entries");
    
    // Generate filename with date
    const fileName = `Milk_Entries_${date.replace(/\//g, '-')}_${shift}.xlsx`;
    
    // Write and download
    XLSX.writeFile(wb, fileName);
  };

  // Add autocomplete functionality for supplier names
  const [showSupplierDropdown, setShowSupplierDropdown] = useState(false);
  const [supplierSearchTerm, setSupplierSearchTerm] = useState('');
  const supplierDropdownRef = useRef(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event) {
      if (supplierDropdownRef.current && !supplierDropdownRef.current.contains(event.target)) {
        setShowSupplierDropdown(false);
      }
    }
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Update supplier search term when name changes
  useEffect(() => {
    setSupplierSearchTerm(form.name);
  }, [form.name]);

  // Filter suppliers based on search term
  const matchingSuppliers = filteredSuppliers.filter(supplier => 
    supplier.name.toLowerCase().includes(supplierSearchTerm.toLowerCase())
  ).slice(0, 10); // Limit to 10 results for performance

  const handleSupplierSelect = (supplier) => {
    // Update form with selected supplier data
    setForm({
      ...form,
      name: supplier.name,
      supplierCode: supplier.code,
      rate: supplier.milk_rate || form.rate // Use supplier's rate if available
    });
    setShowSupplierDropdown(false);
    
    // Move focus to the next field
    inputRefs.kgs.current.focus();
  };

  const handleNameInputChange = (e) => {
    const value = e.target.value;
    setForm({ ...form, name: value });
    setSupplierSearchTerm(value);
    setShowSupplierDropdown(true);
  };

  // Add function to handle row click for editing
  const handleEditEntry = (entry, index) => {
    // Set form data with the selected entry
    setForm({
      name: entry.name,
      kgs: entry.kgs,
      ltrs: entry.ltrs,
      fat: entry.fat,
      snf: entry.snf,
      ts: entry.ts,
      rate: entry.rate,
      splRate: entry.splRate || '',
      amount: entry.amount,
      supplierCode: entry.supplierCode || ''
    });
    
    // Set editing state
    setIsEditing(true);
    setEditingIndex(index);
    
    // Focus on the first field
    setTimeout(() => {
      inputRefs.name.current.focus();
    }, 0);
  };

  // Add function to cancel editing
  const handleCancelEdit = () => {
    setForm({ 
      name: '', 
      kgs: '', 
      ltrs: '', 
      fat: '', 
      snf: '', 
      ts: '', 
      rate: '', 
      splRate: '', 
      amount: '', 
      supplierCode: '' 
    });
    setIsEditing(false);
    setEditingIndex(null);
  };

  // Add function to delete an entry
  const handleDeleteEntry = (index) => {
    if (window.confirm("Are you sure you want to delete this entry?")) {
      const updatedEntries = [...entries];
      updatedEntries.splice(index, 1);
      setEntries(updatedEntries);
      
      // If currently editing this entry, reset the form
      if (isEditing && editingIndex === index) {
        handleCancelEdit();
      }
    }
  };

  return (
    <div className="p-6">
      <div className="text-center font-bold text-xl mb-6 text-blue-800">MILK PURCHASE ENTRY SYSTEM</div>
      
      <div className="flex flex-wrap items-center gap-4 mb-4">
        {/* Date picker */}
        <div className="flex-1 min-w-[200px]">
          <label className="block text-sm font-medium text-gray-700 mb-1">Date</label>
          <input
            type="date"
            value={date}
            onChange={(e) => setDate(e.target.value)}
            className="w-full border border-gray-300 px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
        
        {/* GRN Number */}
        <div className="flex-1 min-w-[200px]">
          <label className="block text-sm font-medium text-gray-700 mb-1">GRN No</label>
          <input
            type="text"
            value={grnNo}
            onChange={(e) => setGrnNo(e.target.value)}
            className="w-full border border-gray-300 px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            placeholder="Enter GRN Number"
          />
        </div>
        
        {/* Route selector */}
        <div className="flex-1 min-w-[200px]">
          <label className="block text-sm font-medium text-gray-700 mb-1">Route</label>
          <select
            value={route}
            onChange={(e) => setRoute(e.target.value)}
            className="w-full border border-gray-300 px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">Select Route</option>
            {routes.map((route) => (
              <option key={route.id} value={route.code}>
                {route.route_name}
              </option>
            ))}
          </select>
        </div>
        
        {/* Shift selector */}
        <div className="flex-1 min-w-[200px]">
          <label className="block text-sm font-medium text-gray-700 mb-1">Shift</label>
          <select
            value={shift}
            onChange={(e) => setShift(e.target.value)}
            className="w-full border border-gray-300 px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="Morning">Morning</option>
            <option value="Evening">Evening</option>
          </select>
        </div>
        
        {/* TS List Type selector */}
        <div className="flex-1 min-w-[200px]">
          <label className="block text-sm font-medium text-gray-700 mb-1">TS List Type</label>
          <select
            value={selectedListType}
            onChange={(e) => setSelectedListType(e.target.value)}
            className="w-full border border-gray-300 px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            {listTypes.map((type) => (
              <option key={type.id} value={type.type_id}>
                {type.type_name}
              </option>
            ))}
          </select>
        </div>
      </div>
      
      {/* Entry Form */}
      <div className="bg-gray-100 p-4 rounded-md mb-6">
        <div className="grid grid-cols-10 gap-2 mb-2">
          <div className="font-semibold text-sm text-gray-700">Name</div>
          <div className="font-semibold text-sm text-gray-700">KGs</div>
          <div className="font-semibold text-sm text-gray-700">LTRs</div>
          <div className="font-semibold text-sm text-gray-700">FAT</div>
          <div className="font-semibold text-sm text-gray-700">SNF</div>
          <div className="font-semibold text-sm text-gray-700">TS</div>
          <div className="font-semibold text-sm text-gray-700">Rate</div>
          <div className="font-semibold text-sm text-gray-700">Spl Rate</div>
          <div className="font-semibold text-sm text-gray-700">Amount</div>
        </div>
        
        <div className="grid grid-cols-10 gap-2 mb-4">
          <div className="relative">
            <input 
              ref={inputRefs.name}
              name="name" 
              value={form.name} 
              onChange={handleNameInputChange}
              onKeyDown={(e) => {
                if (e.key === 'ArrowDown' && matchingSuppliers.length > 0) {
                  // Focus the first item in dropdown
                  document.getElementById('supplier-item-0')?.focus();
                  return;
                }
                handleKeyDown(e, 'name');
              }}
              onClick={() => setShowSupplierDropdown(true)}
              className="w-full border border-gray-300 px-2 py-1 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Select supplier"
            />
            
            {showSupplierDropdown && supplierSearchTerm && (
              <div 
                ref={supplierDropdownRef}
                className="absolute z-10 mt-1 w-full bg-white shadow-lg max-h-60 rounded-md py-1 text-base overflow-auto focus:outline-none sm:text-sm"
              >
                {matchingSuppliers.length > 0 ? (
                  matchingSuppliers.map((supplier, index) => (
                    <div
                      id={`supplier-item-${index}`}
                      key={supplier.id}
                      onClick={() => handleSupplierSelect(supplier)}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') handleSupplierSelect(supplier);
                        if (e.key === 'ArrowDown') document.getElementById(`supplier-item-${Math.min(index + 1, matchingSuppliers.length - 1)}`)?.focus();
                        if (e.key === 'ArrowUp') {
                          if (index === 0) inputRefs.name.current.focus();
                          else document.getElementById(`supplier-item-${index - 1}`)?.focus();
                        }
                      }}
                      tabIndex="0"
                      className="cursor-pointer select-none relative py-2 pl-3 pr-9 hover:bg-blue-100 focus:bg-blue-100 focus:outline-none"
                    >
                      <div className="flex items-center">
                        <span className="font-normal block truncate">{supplier.name}</span>
                        <span className="ml-2 text-xs text-gray-500">({supplier.code})</span>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="cursor-default select-none relative py-2 pl-3 pr-9 text-gray-500">
                    No suppliers found
                  </div>
                )}
              </div>
            )}
          </div>
          <input 
            ref={inputRefs.kgs}
            name="kgs" 
            value={form.kgs} 
            onChange={handleChange}
            onKeyDown={(e) => handleKeyDown(e, 'kgs')}
            type="number"
            className="border border-gray-300 px-2 py-1 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          {/* LTRs field - read-only */}
          <input 
            ref={inputRefs.ltrs}
            name="ltrs" 
            value={form.ltrs} 
            onChange={handleChange}
            onKeyDown={(e) => handleKeyDown(e, 'ltrs')}
            type="number"
            readOnly
            className="border border-gray-300 px-2 py-1 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 bg-gray-50"
            tabIndex="-1" // Remove from tab order
          />
          <input 
            ref={inputRefs.fat}
            name="fat" 
            value={form.fat} 
            onChange={handleChange}
            onKeyDown={(e) => handleKeyDown(e, 'fat')}
            type="number"
            className="border border-gray-300 px-2 py-1 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          <input 
            ref={inputRefs.snf}
            name="snf" 
            value={form.snf} 
            onChange={handleChange}
            onKeyDown={(e) => handleKeyDown(e, 'snf')}
            type="number"
            className="border border-gray-300 px-2 py-1 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          <input 
            ref={inputRefs.ts}
            name="ts" 
            value={form.ts} 
            onChange={handleChange}
            onKeyDown={(e) => handleKeyDown(e, 'ts')}
            type="number"
            readOnly
            className="border border-gray-300 px-2 py-1 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 bg-gray-50"
            tabIndex="-1" // Remove from tab order
          />
          <input 
            ref={inputRefs.rate}
            name="rate" 
            value={form.rate} 
            onChange={handleChange}
            onKeyDown={(e) => handleKeyDown(e, 'rate')}
            type="number"
            readOnly
            className="border border-gray-300 px-2 py-1 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 bg-gray-50"
            tabIndex="-1" // Remove from tab order
          />
          <input 
            ref={inputRefs.splRate}
            name="splRate" 
            value={form.splRate} 
            onChange={handleChange}
            onKeyDown={(e) => handleKeyDown(e, 'splRate')}
            type="number"
            className="border border-gray-300 px-2 py-1 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          <input 
            ref={inputRefs.amount}
            name="amount" 
            value={form.amount} 
            readOnly
            onKeyDown={(e) => handleKeyDown(e, 'amount')}
            className="border border-gray-300 px-2 py-1 rounded bg-gray-50 text-right font-semibold"
            tabIndex="-1" // Remove from tab order
          />
        </div>
        
        <div className="flex justify-center mt-4">
          <button 
            onClick={handleAdd}
            className={`${isEditing ? 'bg-blue-600' : 'bg-green-600'} text-white px-4 py-2 rounded hover:${isEditing ? 'bg-blue-700' : 'bg-green-700'} transition-colors mr-2`}
          >
            {isEditing ? 'Update Entry' : 'Add Entry'}
          </button>
          
          {isEditing && (
            <button 
              onClick={handleCancelEdit}
              className="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700 transition-colors"
            >
              Cancel
            </button>
          )}
        </div>
      </div>
      
      {/* Entries Table */}
      {entries.length > 0 && (
        <div className="overflow-x-auto">
          <table className="min-w-full bg-white border border-gray-300">
            <thead>
              <tr className="bg-gray-100">
                <th className="py-2 px-3 border-b text-left">Name</th>
                <th className="py-2 px-3 border-b text-right">KGs</th>
                <th className="py-2 px-3 border-b text-right">LTRs</th>
                <th className="py-2 px-3 border-b text-right">FAT</th>
                <th className="py-2 px-3 border-b text-right">SNF</th>
                <th className="py-2 px-3 border-b text-right">TS</th>
                <th className="py-2 px-3 border-b text-right">Rate</th>
                <th className="py-2 px-3 border-b text-right">Spl Rate</th>
                <th className="py-2 px-3 border-b text-right">Amount</th>
                <th className="py-2 px-3 border-b text-center">Actions</th>
              </tr>
            </thead>
            <tbody>
              {entries.map((entry, index) => (
                <tr 
                  key={index} 
                  className={`${index % 2 === 0 ? 'bg-gray-50' : 'bg-white'} ${editingIndex === index ? 'bg-blue-50' : ''} hover:bg-blue-50 cursor-pointer`}
                  onClick={() => handleEditEntry(entry, index)}
                >
                  <td className="py-2 px-3 border-b">{entry.name}</td>
                  <td className="py-2 px-3 border-b text-right">{entry.kgs}</td>
                  <td className="py-2 px-3 border-b text-right">{entry.ltrs}</td>
                  <td className="py-2 px-3 border-b text-right">{entry.fat}</td>
                  <td className="py-2 px-3 border-b text-right">{entry.snf}</td>
                  <td className="py-2 px-3 border-b text-right">{entry.ts}</td>
                  <td className="py-2 px-3 border-b text-right">{entry.rate}</td>
                  <td className="py-2 px-3 border-b text-right">{entry.splRate}</td>
                  <td className="py-2 px-3 border-b text-right font-semibold">{entry.amount}</td>
                  <td className="py-2 px-3 border-b text-center">
                    <button
                      onClick={(e) => {
                        e.stopPropagation(); // Prevent row click
                        handleDeleteEntry(index);
                      }}
                      className="text-red-600 hover:text-red-800"
                      title="Delete Entry"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
      
      {/* Action Buttons */}
      <div className="flex justify-end mt-6 space-x-4">
        <button 
          onClick={saveEntriesToDatabase}
          className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 transition-colors"
        >
          Save Entries
        </button>
        <button 
          onClick={exportToExcel}
          className="bg-indigo-600 text-white px-4 py-2 rounded hover:bg-indigo-700 transition-colors"
        >
          Export Excel
        </button>
        <button className="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700 transition-colors">
          Print
        </button>
        <button 
          onClick={() => {
            setEntries([]);
            setForm({ name: '', kgs: '', ltrs: '', fat: '', snf: '', ts: '', rate: '', splRate: '', amount: '' });
          }}
          className="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700 transition-colors"
        >
          Clear All
        </button>
      </div>
    </div>
  );
}

export default MilkEntry;
