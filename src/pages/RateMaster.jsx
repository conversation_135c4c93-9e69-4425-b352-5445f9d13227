import React, { useState, useEffect } from 'react';

function RateMaster() {
  const [rates, setRates] = useState([]);
  const [routes, setRoutes] = useState([]);
  const [selectedRate, setSelectedRate] = useState(null);
  const [formData, setFormData] = useState({
    routeName: '',
    listType: 'standard', // Default list type
    fromTS: '',
    toTS: '',
    rate: ''
  });
  const [isEditing, setIsEditing] = useState(false);
  const [applyToAllRoutes, setApplyToAllRoutes] = useState(false);
  const [activeListType, setActiveListType] = useState('standard');
  
  // Add state for TS list types
  const [listTypes, setListTypes] = useState([]);
  
  // Add state for new list type form
  const [newListType, setNewListType] = useState({ typeId: '', typeName: '' });
  const [showNewListTypeForm, setShowNewListTypeForm] = useState(false);

  useEffect(() => {
    loadRoutes();
    loadTSListTypes();
    loadRates();
  }, []);

  const loadRoutes = async () => {
    if (window.electronAPI) {
      try {
        const data = await window.electronAPI.getRoutes();
        setRoutes(data || []);
      } catch (error) {
        console.error('Failed to load routes:', error);
      }
    }
  };

  const loadTSListTypes = async () => {
    if (window.electronAPI) {
      try {
        const data = await window.electronAPI.getTSListTypes();
        setListTypes(data || []);
        
        // If no active list type is set, use the first one
        if (data && data.length > 0 && !activeListType) {
          setActiveListType(data[0].type_id);
        }
      } catch (error) {
        console.error('Failed to load TS list types:', error);
      }
    }
  };

  const loadRates = async (routeName = null, listType = activeListType) => {
    if (window.electronAPI) {
      try {
        const data = await window.electronAPI.getRates(routeName, listType);
        setRates(data || []);
      } catch (error) {
        console.error('Failed to load rates:', error);
      }
    }
  };
  
  // Add function to save a new list type
  const saveNewListType = async () => {
    if (!newListType.typeId || !newListType.typeName) {
      alert("Please enter both ID and Name for the new list type");
      return;
    }
    
    // Check if ID already exists
    if (listTypes.some(type => type.type_id === newListType.typeId)) {
      alert("A list type with this ID already exists");
      return;
    }
    
    try {
      await window.electronAPI.saveTSListType(newListType);
      
      // Reset form and hide it
      setNewListType({ typeId: '', typeName: '' });
      setShowNewListTypeForm(false);
      
      // Reload list types
      loadTSListTypes();
    } catch (error) {
      console.error('Failed to save list type:', error);
      alert("Failed to save list type: " + error.message);
    }
  };
  
  // Add function to delete a list type
  const deleteListType = async (id) => {
    try {
      if (window.confirm("Are you sure you want to delete this list type?")) {
        await window.electronAPI.deleteTSListType(id);
        
        // Reload list types
        loadTSListTypes();
        
        // If the active list type is deleted, switch to standard
        const deletedType = listTypes.find(type => type.id === id);
        if (deletedType && activeListType === deletedType.type_id) {
          setActiveListType('standard');
          loadRates(formData.routeName, 'standard');
        }
      }
    } catch (error) {
      console.error('Failed to delete list type:', error);
      alert("Failed to delete list type: " + error.message);
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };
  
  const handleNewListTypeChange = (e) => {
    const { name, value } = e.target;
    // For typeId field, convert to lowercase and replace spaces with underscores
    if (name === 'typeId') {
      setNewListType({ 
        ...newListType, 
        [name]: value.toLowerCase().replace(/\s+/g, '_') 
      });
    } else {
      setNewListType({ ...newListType, [name]: value });
    }
  };

  const resetForm = () => {
    setFormData({
      routeName: '',
      listType: activeListType,
      fromTS: '',
      toTS: '',
      rate: ''
    });
    setSelectedRate(null);
    setIsEditing(false);
    setApplyToAllRoutes(false);
  };

  const handleAdd = async () => {
    if (!formData.fromTS || !formData.toTS || !formData.rate) {
      alert("Please fill all required fields");
      return;
    }

    if (parseFloat(formData.fromTS) >= parseFloat(formData.toTS)) {
      alert("'From TS' must be less than 'To TS'");
      return;
    }

    try {
      if (applyToAllRoutes) {
        // Save rate for all routes
        for (const route of routes) {
          const rateData = {
            ...formData,
            routeName: route.route_name,
            listType: formData.listType || activeListType
          };
          await window.electronAPI.saveRate(rateData);
        }
      } else {
        // Save rate for selected route only
        if (!formData.routeName) {
          alert("Please select a route");
          return;
        }
        const rateData = {
          ...formData,
          listType: formData.listType || activeListType
        };
        await window.electronAPI.saveRate(rateData);
      }
      
      resetForm();
      loadRates(formData.routeName, activeListType);
    } catch (error) {
      console.error('Failed to save rate:', error);
      alert("Failed to save rate: " + error.message);
    }
  };

  const handleEdit = (rate) => {
    setFormData({
      id: rate.id,
      routeName: rate.route_name || '',
      listType: rate.list_type || activeListType,
      fromTS: rate.from_ts || '',
      toTS: rate.to_ts || '',
      rate: rate.rate || ''
    });
    setSelectedRate(rate);
    setIsEditing(true);
  };

  const handleUpdate = async () => {
    if (!formData.routeName || !formData.fromTS || !formData.toTS || !formData.rate) {
      alert("Please fill all required fields");
      return;
    }

    if (parseFloat(formData.fromTS) >= parseFloat(formData.toTS)) {
      alert("'From TS' must be less than 'To TS'");
      return;
    }

    try {
      await window.electronAPI.saveRate(formData);
      resetForm();
      loadRates(formData.routeName, activeListType);
    } catch (error) {
      console.error('Failed to update rate:', error);
      alert("Failed to update rate: " + error.message);
    }
  };

  const handleDelete = async () => {
    if (!selectedRate) return;
    
    if (window.confirm("Are you sure you want to delete this rate?")) {
      try {
        await window.electronAPI.deleteRate(selectedRate.id);
        resetForm();
        loadRates(formData.routeName, activeListType);
      } catch (error) {
        console.error('Failed to delete rate:', error);
        alert("Failed to delete rate: " + error.message);
      }
    }
  };

  const handleRouteChange = (e) => {
    const routeName = e.target.value;
    setFormData({ ...formData, routeName });
    loadRates(routeName, activeListType);
  };

  const handleListTypeChange = (listType) => {
    setActiveListType(listType);
    setFormData({ ...formData, listType });
    loadRates(formData.routeName, listType);
  };

  return (
    <div className="p-6 max-w-6xl mx-auto">
      <div className="bg-gradient-to-r from-blue-500 to-teal-400 p-4 rounded-t-lg shadow-md">
        <h1 className="text-2xl font-bold text-center text-white">RATE MASTER</h1>
      </div>
      
      <div className="bg-white rounded-b-lg shadow-md overflow-hidden">
        {/* List Type Management */}
        <div className="p-4 bg-gray-50 border-b border-gray-200">
          <div className="flex justify-between items-center">
            <h3 className="text-md font-medium text-gray-700">TS List Types</h3>
            <button
              onClick={() => setShowNewListTypeForm(!showNewListTypeForm)}
              className="bg-blue-500 text-white px-3 py-1 rounded-md text-sm hover:bg-blue-600 transition-colors"
            >
              {showNewListTypeForm ? 'Cancel' : 'Add New List Type'}
            </button>
          </div>
          
          {showNewListTypeForm && (
            <div className="mt-3 p-3 border border-gray-200 rounded-md bg-white">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-3">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">List Type ID</label>
                  <input
                    type="text"
                    name="typeId"
                    value={newListType.typeId}
                    onChange={handleNewListTypeChange}
                    className="w-full border border-gray-300 px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="e.g. special_rate"
                  />
                  <p className="text-xs text-gray-500 mt-1">Lowercase with underscores, no spaces</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Display Name</label>
                  <input
                    type="text"
                    name="typeName"
                    value={newListType.typeName}
                    onChange={handleNewListTypeChange}
                    className="w-full border border-gray-300 px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="e.g. Special Rate"
                  />
                </div>
              </div>
              <button
                onClick={saveNewListType}
                className="bg-green-600 text-white px-3 py-1 rounded-md text-sm hover:bg-green-700 transition-colors"
              >
                Save List Type
              </button>
            </div>
          )}
        </div>
        
        {/* List Type Tabs */}
        <div className="flex flex-wrap border-b border-gray-200">
          {listTypes.map((type) => (
            <div key={type.id} className="flex items-center">
              <button
                className={`px-4 py-2 font-medium text-sm focus:outline-none ${
                  activeListType === type.type_id
                    ? 'border-b-2 border-blue-500 text-blue-600'
                    : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
                onClick={() => handleListTypeChange(type.type_id)}
              >
                {type.type_name} TS List
              </button>
              {/* Delete button for custom list types */}
              {type.is_default === 0 && (
                <button
                  onClick={() => deleteListType(type.id)}
                  className="text-red-500 hover:text-red-700 ml-1 p-1"
                  title={`Delete ${type.type_name} list type`}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                </button>
              )}
            </div>
          ))}
        </div>
        
        {/* Form Section */}
        <div className="p-6 border-b border-gray-200">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Route</label>
              <select
                name="routeName"
                value={formData.routeName}
                onChange={handleRouteChange}
                className="w-full border border-gray-300 px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">Select Route</option>
                {routes.map((route) => (
                  <option key={route.id} value={route.route_name}>
                    {route.route_name}
                  </option>
                ))}
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">From TS</label>
              <input
                type="number"
                name="fromTS"
                value={formData.fromTS}
                onChange={handleInputChange}
                step="0.1"
                className="w-full border border-gray-300 px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="e.g. 8.0"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">To TS</label>
              <input
                type="number"
                name="toTS"
                value={formData.toTS}
                onChange={handleInputChange}
                step="0.1"
                className="w-full border border-gray-300 px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="e.g. 8.5"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Rate</label>
              <input
                type="number"
                name="rate"
                value={formData.rate}
                onChange={handleInputChange}
                step="0.01"
                className="w-full border border-gray-300 px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="e.g. 35.50"
              />
            </div>
            
            <div className="flex items-center mt-6">
              <input
                type="checkbox"
                id="applyToAllRoutes"
                checked={applyToAllRoutes}
                onChange={() => setApplyToAllRoutes(!applyToAllRoutes)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="applyToAllRoutes" className="ml-2 block text-sm text-gray-700">
                Apply to all routes
              </label>
            </div>
          </div>
          
          <div className="mt-6 flex justify-center">
            {isEditing ? (
              <div className="flex space-x-3">
                <button
                  onClick={handleUpdate}
                  className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Update Rate
                </button>
                <button
                  onClick={handleDelete}
                  className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
                >
                  Delete
                </button>
                <button
                  onClick={resetForm}
                  className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors"
                >
                  Cancel
                </button>
              </div>
            ) : (
              <button
                onClick={handleAdd}
                className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors"
              >
                Add Rate
              </button>
            )}
          </div>
        </div>
        
        {/* Rates Table */}
        <div className="p-6">
          <h2 className="text-lg font-semibold mb-4">
            {listTypes.find(type => type.type_id === activeListType)?.type_name || 'Standard'} Rate List
          </h2>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    S.No
                  </th>
                  <th scope="col" className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Route
                  </th>
                  <th scope="col" className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    List Type
                  </th>
                  <th scope="col" className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    From TS
                  </th>
                  <th scope="col" className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    To TS
                  </th>
                  <th scope="col" className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Rate
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {rates.length > 0 ? (
                  rates.map((rate, index) => (
                    <tr
                      key={rate.id} 
                      className={`cursor-pointer hover:bg-blue-50 transition-colors ${selectedRate?.id === rate.id ? 'bg-blue-100' : ''}`}
                      onClick={() => handleEdit(rate)}
                    >
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-center text-gray-900">{index + 1}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-center text-gray-900">{rate.route_name}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-center text-gray-900">
                        {listTypes.find(type => type.type_id === rate.list_type)?.type_name || 'Standard'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-center text-gray-900">{parseFloat(rate.from_ts).toFixed(1)}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-center text-gray-900">{parseFloat(rate.to_ts).toFixed(1)}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-center text-gray-900">{parseFloat(rate.rate).toFixed(2)}</td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan="6" className="px-6 py-4 text-center text-sm text-gray-500">
                      {formData.routeName ? `No ${listTypes.find(type => type.type_id === activeListType)?.type_name || 'Standard'} rates defined for this route` : "Please select a route"}
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
}

export default RateMaster;
