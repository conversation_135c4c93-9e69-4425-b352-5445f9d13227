import React, { useState, useEffect } from 'react';
import * as XLSX from 'xlsx';
import { FiCalendar, FiDownload, FiFilter, FiPrinter } from 'react-icons/fi';

function Reports() {
  const [dateRange, setDateRange] = useState({
    startDate: new Date().toISOString().split('T')[0],
    endDate: new Date().toISOString().split('T')[0]
  });
  const [shift, setShift] = useState('All');
  const [route, setRoute] = useState('');
  const [routes, setRoutes] = useState([]);
  const [reportType, setReportType] = useState('daily');
  const [reportData, setReportData] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [summaryData, setSummaryData] = useState(null);

  // Load routes on component mount
  useEffect(() => {
    loadRoutes();
  }, []);

  const loadRoutes = async () => {
    if (window.electronAPI) {
      try {
        const data = await window.electronAPI.getRoutes();
        setRoutes(data || []);
      } catch (error) {
        console.error('Failed to load routes:', error);
      }
    }
  };

  const generateReport = async () => {
    setIsLoading(true);
    try {
      // Fetch report data based on filters
      const filters = {
        startDate: dateRange.startDate,
        endDate: dateRange.endDate,
        shift: shift !== 'All' ? shift : null,
        route: route || null,
        reportType
      };

      const data = await window.electronAPI.getReportData(filters);
      setReportData(data || []);

      // Calculate summary data
      if (data && data.length > 0) {
        const totalKgs = data.reduce((sum, entry) => sum + (parseFloat(entry.kgs) || 0), 0);
        const totalLtrs = data.reduce((sum, entry) => sum + (parseFloat(entry.ltrs) || 0), 0);
        const totalAmount = data.reduce((sum, entry) => sum + (parseFloat(entry.amount) || 0), 0);
        const avgFat = data.reduce((sum, entry) => sum + (parseFloat(entry.fat) || 0), 0) / data.length;
        const avgSnf = data.reduce((sum, entry) => sum + (parseFloat(entry.snf) || 0), 0) / data.length;

        setSummaryData({
          totalEntries: data.length,
          totalKgs: totalKgs.toFixed(2),
          totalLtrs: totalLtrs.toFixed(2),
          totalAmount: totalAmount.toFixed(2),
          avgFat: avgFat.toFixed(2),
          avgSnf: avgSnf.toFixed(2)
        });
      } else {
        setSummaryData(null);
      }
    } catch (error) {
      console.error('Failed to generate report:', error);
      alert('Failed to generate report: ' + error.message);
    } finally {
      setIsLoading(false);
    }
  };

  const exportToExcel = () => {
    if (reportData.length === 0) {
      alert('No data to export');
      return;
    }

    // Create a workbook
    const wb = XLSX.utils.book_new();
    
    // Create header rows with metadata
    const headerData = [
      [`${reportType.toUpperCase()} MILK PURCHASE REPORT`],
      [`Date Range: ${dateRange.startDate} to ${dateRange.endDate}`, '', `Shift: ${shift}`],
      [`Route: ${route ? routes.find(r => r.code === route)?.route_name || route : 'All Routes'}`],
      [''],  // Empty row for spacing
    ];
    
    // Add column headers based on report type
    let columnHeaders = [];
    if (reportType === 'daily') {
      columnHeaders = ['Date', 'Shift', 'Route', 'Supplier', 'KGs', 'LTRs', 'FAT', 'SNF', 'Rate', 'Amount'];
    } else if (reportType === 'supplier') {
      columnHeaders = ['Supplier', 'Total KGs', 'Total LTRs', 'Avg FAT', 'Avg SNF', 'Total Amount'];
    } else if (reportType === 'route') {
      columnHeaders = ['Route', 'Total KGs', 'Total LTRs', 'Avg FAT', 'Avg SNF', 'Total Amount'];
    }
    
    headerData.push(columnHeaders);
    
    // Convert report data to rows
    const reportRows = reportData.map(entry => {
      if (reportType === 'daily') {
        return [
          entry.date,
          entry.shift,
          entry.route_name,
          entry.name,
          parseFloat(entry.kgs) || 0,
          parseFloat(entry.ltrs) || 0,
          parseFloat(entry.fat) || 0,
          parseFloat(entry.snf) || 0,
          parseFloat(entry.rate) || 0,
          parseFloat(entry.amount) || 0
        ];
      } else if (reportType === 'supplier') {
        return [
          entry.name,
          parseFloat(entry.total_kgs) || 0,
          parseFloat(entry.total_ltrs) || 0,
          parseFloat(entry.avg_fat) || 0,
          parseFloat(entry.avg_snf) || 0,
          parseFloat(entry.total_amount) || 0
        ];
      } else if (reportType === 'route') {
        return [
          entry.route_name,
          parseFloat(entry.total_kgs) || 0,
          parseFloat(entry.total_ltrs) || 0,
          parseFloat(entry.avg_fat) || 0,
          parseFloat(entry.avg_snf) || 0,
          parseFloat(entry.total_amount) || 0
        ];
      }
      return [];
    });
    
    // Add summary row
    const summaryRow = ['TOTAL'];
    if (reportType === 'daily') {
      summaryRow.push(
        '', '', '',
        summaryData.totalKgs,
        summaryData.totalLtrs,
        '', '',
        '',
        summaryData.totalAmount
      );
    } else {
      summaryRow.push(
        summaryData.totalKgs,
        summaryData.totalLtrs,
        '', '',
        summaryData.totalAmount
      );
    }
    
    // Combine all rows
    const allData = [...headerData, ...reportRows, summaryRow];
    
    // Create worksheet
    const ws = XLSX.utils.aoa_to_sheet(allData);
    
    // Set column widths
    const colWidths = columnHeaders.map(() => ({ wch: 15 }));
    ws['!cols'] = colWidths;
    
    // Add worksheet to workbook
    XLSX.utils.book_append_sheet(wb, ws, "Report");
    
    // Generate filename with date range
    const fileName = `Milk_Report_${dateRange.startDate}_to_${dateRange.endDate}.xlsx`;
    
    // Write and download
    XLSX.writeFile(wb, fileName);
  };

  const printReport = () => {
    window.print();
  };

  return (
    <div className="p-6 max-w-7xl mx-auto">
      <div className="bg-gradient-to-r from-purple-600 to-indigo-500 p-4 rounded-t-lg shadow-md">
        <h1 className="text-2xl font-bold text-center text-white">MILK PURCHASE REPORTS</h1>
      </div>
      
      <div className="bg-white rounded-b-lg shadow-md p-6">
        {/* Filters Section */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Report Type</label>
            <select
              value={reportType}
              onChange={(e) => setReportType(e.target.value)}
              className="w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
            >
              <option value="daily">Daily Report</option>
              <option value="supplier">Supplier-wise Report</option>
              <option value="route">Route-wise Report</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <FiCalendar className="text-gray-400" />
              </div>
              <input
                type="date"
                value={dateRange.startDate}
                onChange={(e) => setDateRange({...dateRange, startDate: e.target.value})}
                className="w-full border border-gray-300 rounded-md shadow-sm py-2 pl-10 pr-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              />
            </div>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">End Date</label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <FiCalendar className="text-gray-400" />
              </div>
              <input
                type="date"
                value={dateRange.endDate}
                onChange={(e) => setDateRange({...dateRange, endDate: e.target.value})}
                className="w-full border border-gray-300 rounded-md shadow-sm py-2 pl-10 pr-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              />
            </div>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Shift</label>
            <select
              value={shift}
              onChange={(e) => setShift(e.target.value)}
              className="w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
            >
              <option value="All">All Shifts</option>
              <option value="Morning">Morning</option>
              <option value="Evening">Evening</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Route</label>
            <select
              value={route}
              onChange={(e) => setRoute(e.target.value)}
              className="w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
            >
              <option value="">All Routes</option>
              {routes.map(route => (
                <option key={route.id} value={route.code}>
                  {route.route_name}
                </option>
              ))}
            </select>
          </div>
          
          <div className="flex items-end">
            <button
              onClick={generateReport}
              disabled={isLoading}
              className={`w-full bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition-colors flex items-center justify-center ${
                isLoading ? 'opacity-50 cursor-not-allowed' : ''
              }`}
            >
              <FiFilter className="mr-2" />
              {isLoading ? 'Loading...' : 'Generate Report'}
            </button>
          </div>
        </div>
        
        {/* Summary Cards */}
        {summaryData && (
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-6">
            <div className="bg-gradient-to-br from-blue-50 to-blue-100 p-4 rounded-lg shadow">
              <p className="text-blue-800 text-sm font-medium">Total Entries</p>
              <p className="text-2xl font-bold text-blue-900">{summaryData.totalEntries}</p>
            </div>
            <div className="bg-gradient-to-br from-green-50 to-green-100 p-4 rounded-lg shadow">
              <p className="text-green-800 text-sm font-medium">Total KGs</p>
              <p className="text-2xl font-bold text-green-900">{summaryData.totalKgs}</p>
            </div>
            <div className="bg-gradient-to-br from-purple-50 to-purple-100 p-4 rounded-lg shadow">
              <p className="text-purple-800 text-sm font-medium">Total LTRs</p>
              <p className="text-2xl font-bold text-purple-900">{summaryData.totalLtrs}</p>
            </div>
            <div className="bg-gradient-to-br from-yellow-50 to-yellow-100 p-4 rounded-lg shadow">
              <p className="text-yellow-800 text-sm font-medium">Avg FAT</p>
              <p className="text-2xl font-bold text-yellow-900">{summaryData.avgFat}</p>
            </div>
            <div className="bg-gradient-to-br from-pink-50 to-pink-100 p-4 rounded-lg shadow">
              <p className="text-pink-800 text-sm font-medium">Avg SNF</p>
              <p className="text-2xl font-bold text-pink-900">{summaryData.avgSnf}</p>
            </div>
            <div className="bg-gradient-to-br from-red-50 to-red-100 p-4 rounded-lg shadow">
              <p className="text-red-800 text-sm font-medium">Total Amount</p>
              <p className="text-2xl font-bold text-red-900">₹{summaryData.totalAmount}</p>
            </div>
          </div>
        )}
        
        {/* Action Buttons */}
        {reportData.length > 0 && (
          <div className="flex justify-end mb-4 space-x-2">
            <button
              onClick={exportToExcel}
              className="bg-green-600 text-white px-3 py-1 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-colors flex items-center"
            >
              <FiDownload className="mr-1" />
              Export Excel
            </button>
            <button
              onClick={printReport}
              className="bg-blue-600 text-white px-3 py-1 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors flex items-center"
            >
              <FiPrinter className="mr-1" />
              Print
            </button>
          </div>
        )}
        
        {/* Report Table */}
        {reportData.length > 0 ? (
          <div className="overflow-x-auto print:overflow-visible">
            <table className="min-w-full divide-y divide-gray-200 border border-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  {reportType === 'daily' && (
                    <>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Shift</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Route</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Supplier</th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">KGs</th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">LTRs</th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">FAT</th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">SNF</th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Rate</th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                    </>
                  )}
                  
                  {reportType === 'supplier' && (
                    <>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Supplier</th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Total KGs</th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Total LTRs</th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Avg FAT</th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Avg SNF</th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Total Amount</th>
                    </>
                  )}
                  
                  {reportType === 'route' && (
                    <>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Route</th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Total KGs</th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Total LTRs</th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Avg FAT</th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Avg SNF</th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Total Amount</th>
                    </>
                  )}
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {reportData.map((entry, index) => (
                  <tr key={index} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                    {reportType === 'daily' && (
                      <>
                        <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-900">{entry.date}</td>
                        <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-900">{entry.shift}</td>
                        <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-900">{entry.route_name}</td>
                        <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-900">{entry.name}</td>
                        <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-900 text-right">{parseFloat(entry.kgs).toFixed(2)}</td>
                        <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-900 text-right">{parseFloat(entry.ltrs).toFixed(2)}</td>
                        <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-900 text-right">{parseFloat(entry.fat).toFixed(2)}</td>
                        <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-900 text-right">{parseFloat(entry.snf).toFixed(2)}</td>
                        <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-900 text-right">{parseFloat(entry.rate).toFixed(2)}</td>
                        <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-900 text-right">{parseFloat(entry.amount).toFixed(2)}</td>
                      </>
                    )}
                    
                    {reportType === 'supplier' && (
                      <>
                        <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-900">{entry.name}</td>
                        <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-900 text-right">{parseFloat(entry.total_kgs).toFixed(2)}</td>
                        <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-900 text-right">{parseFloat(entry.total_ltrs).toFixed(2)}</td>
                        <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-900 text-right">{parseFloat(entry.avg_fat).toFixed(2)}</td>
                        <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-900 text-right">{parseFloat(entry.avg_snf).toFixed(2)}</td>
                        <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-900 text-right">{parseFloat(entry.total_amount).toFixed(2)}</td>
                      </>
                    )}
                    
                    {reportType === 'route' && (
                      <>
                        <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-900">{entry.route_name}</td>
                        <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-900 text-right">{parseFloat(entry.total_kgs).toFixed(2)}</td>
                        <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-900 text-right">{parseFloat(entry.total_ltrs).toFixed(2)}</td>
                        <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-900 text-right">{parseFloat(entry.avg_fat).toFixed(2)}</td>
                        <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-900 text-right">{parseFloat(entry.avg_snf).toFixed(2)}</td>
                        <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-900 text-right">{parseFloat(entry.total_amount).toFixed(2)}</td>
                      </>
                    )}
                  </tr>
                ))}
              </tbody>
              <tfoot className="bg-gray-100">
                <tr className="font-semibold">
                  {reportType === 'daily' && (
                    <>
                      <td colSpan="4" className="px-4 py-3 text-sm text-gray-900">TOTAL</td>
                      <td className="px-4 py-3 text-sm text-gray-900 text-right">{summaryData?.totalKgs}</td>
                      <td className="px-4 py-3 text-sm text-gray-900 text-right">{summaryData?.totalLtrs}</td>
                      <td className="px-4 py-3 text-sm text-gray-900 text-right">{summaryData?.avgFat}</td>
                      <td className="px-4 py-3 text-sm text-gray-900 text-right">{summaryData?.avgSnf}</td>
                      <td className="px-4 py-3 text-sm text-gray-900 text-right"></td>
                      <td className="px-4 py-3 text-sm text-gray-900 text-right">{summaryData?.totalAmount}</td>
                    </>
                  )}
                  
                  {(reportType === 'supplier' || reportType === 'route') && (
                    <>
                      <td className="px-4 py-3 text-sm text-gray-900">TOTAL</td>
                      <td className="px-4 py-3 text-sm text-gray-900 text-right">{summaryData?.totalKgs}</td>
                      <td className="px-4 py-3 text-sm text-gray-900 text-right">{summaryData?.totalLtrs}</td>
                      <td className="px-4 py-3 text-sm text-gray-900 text-right">{summaryData?.avgFat}</td>
                      <td className="px-4 py-3 text-sm text-gray-900 text-right">{summaryData?.avgSnf}</td>
                      <td className="px-4 py-3 text-sm text-gray-900 text-right">{summaryData?.totalAmount}</td>
                    </>
                  )}
                </tr>
              </tfoot>
            </table>
          </div>
        ) : (
          !isLoading && (
            <div className="text-center py-10">
              <div className="text-gray-500 mb-2">No report data to display</div>
              <div className="text-sm text-gray-400">Adjust filters and click Generate Report</div>
            </div>
          )
        )}
        
        {/* Daily Exports Section */}
        <div className="mt-10">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">Daily Exports</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {/* Morning Shift Card */}
            <div className="bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow">
              <div className="bg-gradient-to-r from-yellow-400 to-orange-500 p-3 rounded-t-lg">
                <h3 className="text-white font-semibold">Morning Shift Export</h3>
              </div>
              <div className="p-4">
                <div className="mb-3">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Date</label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <FiCalendar className="text-gray-400" />
                    </div>
                    <input
                      type="date"
                      value={dateRange.startDate}
                      onChange={(e) => setDateRange({...dateRange, startDate: e.target.value})}
                      className="w-full border border-gray-300 rounded-md shadow-sm py-2 pl-10 pr-3 focus:outline-none focus:ring-orange-500 focus:border-orange-500"
                    />
                  </div>
                </div>
                <button
                  onClick={() => {
                    setShift('Morning');
                    setDateRange({...dateRange, endDate: dateRange.startDate});
                    setReportType('daily');
                    setTimeout(() => {
                      generateReport().then(() => {
                        if (reportData.length > 0) {
                          exportToExcel();
                        }
                      });
                    }, 100);
                  }}
                  className="w-full bg-orange-500 text-white px-4 py-2 rounded-md hover:bg-orange-600 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 transition-colors flex items-center justify-center"
                >
                  <FiDownload className="mr-2" />
                  Export Morning Data
                </button>
              </div>
            </div>
            
            {/* Evening Shift Card */}
            <div className="bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow">
              <div className="bg-gradient-to-r from-blue-400 to-indigo-500 p-3 rounded-t-lg">
                <h3 className="text-white font-semibold">Evening Shift Export</h3>
              </div>
              <div className="p-4">
                <div className="mb-3">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Date</label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <FiCalendar className="text-gray-400" />
                    </div>
                    <input
                      type="date"
                      value={dateRange.startDate}
                      onChange={(e) => setDateRange({...dateRange, startDate: e.target.value})}
                      className="w-full border border-gray-300 rounded-md shadow-sm py-2 pl-10 pr-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                    />
                  </div>
                </div>
                <button
                  onClick={() => {
                    setShift('Evening');
                    setDateRange({...dateRange, endDate: dateRange.startDate});
                    setReportType('daily');
                    setTimeout(() => {
                      generateReport().then(() => {
                        if (reportData.length > 0) {
                          exportToExcel();
                        }
                      });
                    }, 100);
                  }}
                  className="w-full bg-indigo-500 text-white px-4 py-2 rounded-md hover:bg-indigo-600 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition-colors flex items-center justify-center"
                >
                  <FiDownload className="mr-2" />
                  Export Evening Data
                </button>
              </div>
            </div>
            
            {/* Full Day Card */}
            <div className="bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow">
              <div className="bg-gradient-to-r from-green-400 to-teal-500 p-3 rounded-t-lg">
                <h3 className="text-white font-semibold">Full Day Export</h3>
              </div>
              <div className="p-4">
                <div className="mb-3">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Date</label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <FiCalendar className="text-gray-400" />
                    </div>
                    <input
                      type="date"
                      value={dateRange.startDate}
                      onChange={(e) => setDateRange({...dateRange, startDate: e.target.value})}
                      className="w-full border border-gray-300 rounded-md shadow-sm py-2 pl-10 pr-3 focus:outline-none focus:ring-teal-500 focus:border-teal-500"
                    />
                  </div>
                </div>
                <button
                  onClick={() => {
                    setShift('All');
                    setDateRange({...dateRange, endDate: dateRange.startDate});
                    setReportType('daily');
                    setTimeout(() => {
                      generateReport().then(() => {
                        if (reportData.length > 0) {
                          exportToExcel();
                        }
                      });
                    }, 100);
                  }}
                  className="w-full bg-teal-500 text-white px-4 py-2 rounded-md hover:bg-teal-600 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2 transition-colors flex items-center justify-center"
                >
                  <FiDownload className="mr-2" />
                  Export Full Day Data
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default Reports;
