import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>ser, FiPhone, FiMapPin, FiHash, FiSearch, FiEdit, FiTrash2, FiPlus, FiX, FiSave } from 'react-icons/fi';

function CustomerMaster() {
  const [customers, setCustomers] = useState([]);
  const [selectedCustomer, setSelectedCustomer] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [formData, setFormData] = useState({
    code: '',
    customerId: '',
    customerName: '',
    address1: '',
    address2: '',
    place: '',
    taluk: '',
    pinCode: '',
    phoneNo: '',
    cellNo: '',
    aadhaarNumber: '',
    gstinNo: '',
    accountLock: false
  });
  const [isEditing, setIsEditing] = useState(false);

  useEffect(() => {
    loadCustomers();
  }, []);

  const loadCustomers = async () => {
    if (window.electronAPI) {
      try {
        const data = await window.electronAPI.getCustomers();
        setCustomers(data || []);
      } catch (error) {
        console.error('Failed to load customers:', error);
      }
    }
  };

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({ 
      ...formData, 
      [name]: type === 'checkbox' ? checked : value 
    });
  };

  const resetForm = () => {
    setFormData({
      code: '',
      customerId: '',
      customerName: '',
      address1: '',
      address2: '',
      place: '',
      taluk: '',
      pinCode: '',
      phoneNo: '',
      cellNo: '',
      aadhaarNumber: '',
      gstinNo: '',
      accountLock: false
    });
    setSelectedCustomer(null);
    setIsEditing(false);
  };

  const handleAdd = async () => {
    if (!formData.customerName) {
      alert("Please enter customer name");
      return;
    }

    try {
      await window.electronAPI.saveCustomer(formData);
      resetForm();
      loadCustomers();
    } catch (error) {
      console.error('Failed to save customer:', error);
      alert("Failed to save customer: " + error.message);
    }
  };

  const handleEdit = (customer) => {
    setFormData({
      id: customer.id,
      code: customer.code || '',
      customerId: customer.customer_id || '',
      customerName: customer.customer_name || '',
      address1: customer.address1 || '',
      address2: customer.address2 || '',
      place: customer.place || '',
      taluk: customer.taluk || '',
      pinCode: customer.pin_code || '',
      phoneNo: customer.phone_no || '',
      cellNo: customer.cell_no || '',
      aadhaarNumber: customer.aadhaar_number || '',
      gstinNo: customer.gstin_no || '',
      accountLock: customer.account_lock || false
    });
    setSelectedCustomer(customer);
    setIsEditing(true);
  };

  const handleUpdate = async () => {
    if (!formData.customerName) {
      alert("Please enter customer name");
      return;
    }

    try {
      await window.electronAPI.saveCustomer(formData);
      resetForm();
      loadCustomers();
    } catch (error) {
      console.error('Failed to update customer:', error);
      alert("Failed to update customer: " + error.message);
    }
  };

  const handleDelete = async () => {
    if (!selectedCustomer) return;
    
    if (window.confirm("Are you sure you want to delete this customer?")) {
      try {
        await window.electronAPI.deleteCustomer(selectedCustomer.id);
        resetForm();
        loadCustomers();
      } catch (error) {
        console.error('Failed to delete customer:', error);
        alert("Failed to delete customer: " + error.message);
      }
    }
  };

  const filteredCustomers = customers.filter(customer => 
    customer.customer_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    customer.code?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="p-6 bg-gray-50">
      <div className="bg-gradient-to-r from-blue-600 to-teal-500 p-5 rounded-t-lg shadow-lg">
        <h1 className="text-2xl font-bold text-center text-white tracking-wide">
          CUSTOMER ACCOUNT ENTRY SYSTEM
        </h1>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mt-6">
        {/* Customer List */}
        <div className="bg-white p-5 rounded-lg shadow-md lg:col-span-1">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold text-gray-800">Customer List</h2>
            <button 
              onClick={resetForm}
              className="bg-blue-500 hover:bg-blue-600 text-white p-2 rounded-full transition-colors"
              title="Add New Customer"
            >
              <FiPlus className="h-5 w-5" />
            </button>
          </div>
          
          <div className="relative mb-4">
            <input
              type="text"
              placeholder="Search customers..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
            <FiSearch className="absolute left-3 top-2.5 text-gray-400 h-5 w-5" />
          </div>
          
          <div className="overflow-auto max-h-[calc(100vh-280px)] rounded-lg border border-gray-200">
            {filteredCustomers.length > 0 ? (
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50 sticky top-0">
                  <tr>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer Name</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredCustomers.map((customer) => (
                    <tr 
                      key={customer.id} 
                      className={`cursor-pointer hover:bg-blue-50 ${selectedCustomer?.id === customer.id ? 'bg-blue-100' : ''}`}
                      onClick={() => handleEdit(customer)}
                    >
                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900">{customer.code}</td>
                      <td className="px-4 py-3 text-sm text-gray-900">{customer.customer_name}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            ) : (
              <div className="text-center py-4 text-gray-500">
                {searchTerm ? "No customers found" : "No customers added yet"}
              </div>
            )}
          </div>
        </div>
        
        {/* Customer Form */}
        <div className="bg-white p-5 rounded-lg shadow-md lg:col-span-2">
          <h2 className="text-xl font-semibold mb-4 text-gray-800 border-b pb-2">
            {isEditing ? 'Edit Customer' : 'Add New Customer'}
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
            <div className="space-y-1">
              <label className="block text-sm font-medium text-gray-700">Code</label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FiHash className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  name="code"
                  value={formData.code}
                  onChange={handleInputChange}
                  className="pl-10 w-full border border-gray-300 px-3 py-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>
            
            <div className="space-y-1">
              <label className="block text-sm font-medium text-gray-700">Customer ID</label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FiHash className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  name="customerId"
                  value={formData.customerId}
                  onChange={handleInputChange}
                  className="pl-10 w-full border border-gray-300 px-3 py-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>
            
            <div className="space-y-1 md:col-span-2">
              <label className="block text-sm font-medium text-gray-700">Customer Name</label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FiUser className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  name="customerName"
                  value={formData.customerName}
                  onChange={handleInputChange}
                  className="pl-10 w-full border border-gray-300 px-3 py-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>
            
            <div className="space-y-1 md:col-span-2">
              <label className="block text-sm font-medium text-gray-700">Address 1</label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FiMapPin className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  name="address1"
                  value={formData.address1}
                  onChange={handleInputChange}
                  className="pl-10 w-full border border-gray-300 px-3 py-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>
            
            <div className="space-y-1 md:col-span-2">
              <label className="block text-sm font-medium text-gray-700">Address 2</label>
              <input
                type="text"
                name="address2"
                value={formData.address2}
                onChange={handleInputChange}
                className="w-full border border-gray-300 px-3 py-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            
            <div className="space-y-1">
              <label className="block text-sm font-medium text-gray-700">Place</label>
              <input
                type="text"
                name="place"
                value={formData.place}
                onChange={handleInputChange}
                className="w-full border border-gray-300 px-3 py-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            
            <div className="space-y-1">
              <label className="block text-sm font-medium text-gray-700">Taluk</label>
              <input
                type="text"
                name="taluk"
                value={formData.taluk}
                onChange={handleInputChange}
                className="w-full border border-gray-300 px-3 py-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            
            <div className="space-y-1">
              <label className="block text-sm font-medium text-gray-700">Pin Code</label>
              <input
                type="text"
                name="pinCode"
                value={formData.pinCode}
                onChange={handleInputChange}
                className="w-full border border-gray-300 px-3 py-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            
            <div className="space-y-1 flex items-center">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  name="accountLock"
                  checked={formData.accountLock}
                  onChange={handleInputChange}
                  className="h-5 w-5 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <span className="ml-2 text-sm font-medium text-gray-700">Account Lock</span>
              </label>
            </div>
            
            <div className="space-y-1">
              <label className="block text-sm font-medium text-gray-700">Phone No</label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FiPhone className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  name="phoneNo"
                  value={formData.phoneNo}
                  onChange={handleInputChange}
                  className="pl-10 w-full border border-gray-300 px-3 py-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>
            
            <div className="space-y-1">
              <label className="block text-sm font-medium text-gray-700">Cell No</label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FiPhone className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  name="cellNo"
                  value={formData.cellNo}
                  onChange={handleInputChange}
                  className="pl-10 w-full border border-gray-300 px-3 py-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>
            
            <div className="space-y-1">
              <label className="block text-sm font-medium text-gray-700">Aadhaar Number</label>
              <input
                type="text"
                name="aadhaarNumber"
                value={formData.aadhaarNumber}
                onChange={handleInputChange}
                className="w-full border border-gray-300 px-3 py-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            
            <div className="space-y-1">
              <label className="block text-sm font-medium text-gray-700">GSTIN No.</label>
              <input
                type="text"
                name="gstinNo"
                value={formData.gstinNo}
                onChange={handleInputChange}
                className="w-full border border-gray-300 px-3 py-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
          
          <div className="flex justify-center mt-6 space-x-3">
            <button
              onClick={isEditing ? handleUpdate : handleAdd}
              className="flex items-center bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
            >
              {isEditing ? <><FiEdit className="mr-2" /> Update</> : <><FiPlus className="mr-2" /> Add</>}
            </button>
            
            {isEditing && (
              <button
                onClick={handleDelete}
                className="flex items-center bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
              >
                <FiTrash2 className="mr-2" /> Delete
              </button>
            )}
            
            <button
              onClick={resetForm}
              className="flex items-center bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors"
            >
              <FiX className="mr-2" /> Cancel
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

export default CustomerMaster;
