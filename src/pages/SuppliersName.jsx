import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>ser, FiPhone, FiHash, FiSearch, FiEdit, FiTrash2, FiPlus, FiX, FiSave, FiDollarSign, FiTruck } from 'react-icons/fi';

function SuppliersName() {
  const [suppliers, setSuppliers] = useState([]);
  const [selectedSupplier, setSelectedSupplier] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [route, setRoute] = useState('');
  const [routes, setRoutes] = useState([]);
  const [formData, setFormData] = useState({
    code: '',
    route: '',
    name: '',
    tamilName: '',
    cellNo: '',
    bonusAmount: '0.00',
    milkRate: '0.00'
  });
  const [isEditing, setIsEditing] = useState(false);

  useEffect(() => {
    loadRoutes();
    loadSuppliers();
  }, []);

  const loadSuppliers = async () => {
    if (window.electronAPI) {
      try {
        const data = await window.electronAPI.getSuppliers();
        setSuppliers(data || []);
      } catch (error) {
        console.error('Failed to load suppliers:', error);
      }
    }
  };

    const loadRoutes = async () => {
    if (window.electronAPI) {
      try {
        const data = await window.electronAPI.getRoutes();
        setRoutes(data || []);
      } catch (error) {
        console.error('Failed to load routes:', error);
      }
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const resetForm = () => {
    setFormData({
      code: '',
      route: '',
      name: '',
      tamilName: '',
      cellNo: '',
      bonusAmount: '0.00',
      milkRate: '0.00'
    });
    setSelectedSupplier(null);
    setIsEditing(false);
  };

  const handleAdd = async () => {
    if (!formData.name) {
      alert("Please enter supplier's name");
      return;
    }

    try {
      await window.electronAPI.saveSupplier(formData);
      resetForm();
      loadSuppliers();
    } catch (error) {
      console.error('Failed to save supplier:', error);
      alert("Failed to save supplier: " + error.message);
    }
  };

  const handleEdit = (supplier) => {
    setFormData({
      id: supplier.id,
      code: supplier.code || '',
      route: supplier.route || '',
      name: supplier.name || '',
      tamilName: supplier.tamil_name || '',
      cellNo: supplier.cell_no || '',
      bonusAmount: supplier.bonus_amount?.toString() || '0.00',
      milkRate: supplier.milk_rate?.toString() || '0.00'
    });
    setSelectedSupplier(supplier);
    setIsEditing(true);
  };

  const handleUpdate = async () => {
    if (!formData.name) {
      alert("Please enter supplier's name");
      return;
    }

    try {
      await window.electronAPI.saveSupplier(formData);
      resetForm();
      loadSuppliers();
    } catch (error) {
      console.error('Failed to update supplier:', error);
      alert("Failed to update supplier: " + error.message);
    }
  };

  const handleDelete = async () => {
    if (!selectedSupplier) return;

    if (window.confirm("Are you sure you want to delete this supplier?")) {
      try {
        await window.electronAPI.deleteSupplier(selectedSupplier.id);
        resetForm();
        loadSuppliers();
      } catch (error) {
        console.error('Failed to delete supplier:', error);
        alert("Failed to delete supplier: " + error.message);
      }
    }
  };

  const filteredSuppliers = suppliers.filter(supplier =>
    supplier.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    supplier.code?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="p-6 bg-gray-50">
      <div className="bg-gradient-to-r from-blue-600 to-teal-500 p-5 rounded-t-lg shadow-lg">
        <h1 className="text-2xl font-bold text-center text-white tracking-wide">
          SUPPLIER'S NAME ENTRY
        </h1>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mt-6">
        {/* Suppliers List */}
        <div className="bg-white p-5 rounded-lg shadow-md lg:col-span-1">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold text-gray-800">Suppliers List</h2>
            <button
              onClick={resetForm}
              className="bg-blue-500 hover:bg-blue-600 text-white p-2 rounded-full transition-colors"
              title="Add New Supplier"
            >
              <FiPlus className="h-5 w-5" />
            </button>
          </div>

          <div className="relative mb-4">
            <input
              type="text"
              placeholder="Search suppliers..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
            <FiSearch className="absolute left-3 top-2.5 text-gray-400 h-5 w-5" />
          </div>

          <div className="overflow-auto max-h-[calc(100vh-280px)] rounded-lg border border-gray-200">
            {filteredSuppliers.length > 0 ? (
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50 sticky top-0">
                  <tr>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Supplier's Name</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredSuppliers.map((supplier) => (
                    <tr
                      key={supplier.id}
                      className={`cursor-pointer hover:bg-blue-50 ${selectedSupplier?.id === supplier.id ? 'bg-blue-100' : ''}`}
                      onClick={() => handleEdit(supplier)}
                    >
                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900">{supplier.code}</td>
                      <td className="px-4 py-3 text-sm text-gray-900">{supplier.name}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            ) : (
              <div className="text-center py-4 text-gray-500">
                {searchTerm ? "No suppliers found" : "No suppliers added yet"}
              </div>
            )}
          </div>

          <div className="mt-4 text-right text-sm text-gray-500">
            Total Suppliers: {filteredSuppliers.length}
          </div>
        </div>

        {/* Supplier Form */}
        <div className="bg-white p-5 rounded-lg shadow-md lg:col-span-2">
          <h2 className="text-xl font-semibold mb-4 text-gray-800 border-b pb-2">
            {isEditing ? 'Edit Supplier' : 'Add New Supplier'}
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
            <div className="space-y-1">
              <label className="block text-sm font-medium text-gray-700">Code No.</label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FiHash className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  name="code"
                  value={formData.code}
                  onChange={handleInputChange}
                  className="pl-10 w-full border border-gray-300 px-3 py-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>

            <div className="space-y-1">
              <label className="block text-sm font-medium text-gray-700">Route Name</label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FiTruck className="h-5 w-5 text-gray-400" />
                </div>
                <select
                  value={formData.route}
                  onChange={e => {
                    setRoute(e.target.value);
                    setFormData({...formData, route: e.target.value});
                  }}
                  className="border border-gray-300 px-10 py-1 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Select Route</option>
                  {routes.map(route => (
                    <option key={route.id} value={route.code}>
                      {route.route_name}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <div className="space-y-1 md:col-span-2">
              <label className="block text-sm font-medium text-gray-700">Supplier's Name</label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FiUser className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  className="pl-10 w-full border border-gray-300 px-3 py-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>

            <div className="space-y-1 md:col-span-2">
              <label className="block text-sm font-medium text-gray-700">Tamil Name</label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FiUser className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  name="tamilName"
                  value={formData.tamilName}
                  onChange={handleInputChange}
                  className="pl-10 w-full border border-gray-300 px-3 py-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>

            <div className="space-y-1 md:col-span-2">
              <label className="block text-sm font-medium text-gray-700">Cell No</label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FiPhone className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  name="cellNo"
                  value={formData.cellNo}
                  onChange={handleInputChange}
                  className="pl-10 w-full border border-gray-300 px-3 py-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>

            <div className="space-y-1">
              <label className="block text-sm font-medium text-gray-700">Bonus Amount</label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FiDollarSign className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="number"
                  step="0.01"
                  name="bonusAmount"
                  value={formData.bonusAmount}
                  onChange={handleInputChange}
                  className="pl-10 w-full border border-gray-300 px-3 py-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>

            <div className="space-y-1">
              <label className="block text-sm font-medium text-gray-700">Milk Rate</label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FiDollarSign className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="number"
                  step="0.01"
                  name="milkRate"
                  value={formData.milkRate}
                  onChange={handleInputChange}
                  className="pl-10 w-full border border-gray-300 px-3 py-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>
          </div>

          <div className="flex justify-center mt-6 space-x-3">
            <button
              onClick={isEditing ? handleUpdate : handleAdd}
              className="flex items-center bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
            >
              {isEditing ? <><FiEdit className="mr-2" /> Modify</> : <><FiPlus className="mr-2" /> Add</>}
            </button>

            {isEditing && (
              <button
                onClick={handleDelete}
                className="flex items-center bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
              >
                <FiTrash2 className="mr-2" /> Delete
              </button>
            )}

            <button
              onClick={resetForm}
              className="flex items-center bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors"
            >
              <FiX className="mr-2" /> Exit
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

export default SuppliersName;
