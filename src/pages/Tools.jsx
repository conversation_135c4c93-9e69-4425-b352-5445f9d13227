import React, { useState, useRef } from 'react';
import * as XLSX from 'xlsx';
import { FiUpload, FiDownload, FiCheck, FiX } from 'react-icons/fi';

function Tools() {
  const [exportFormat, setExportFormat] = useState('excel');
  const [exportStatus, setExportStatus] = useState('');
  const [isExporting, setIsExporting] = useState(false);
  const [importStatus, setImportStatus] = useState('');
  const [isImporting, setIsImporting] = useState(false);
  const [selectedImportTable, setSelectedImportTable] = useState('');
  const fileInputRef = useRef(null);
  
  const tables = [
    { name: 'Routes', value: 'routes' },
    { name: 'Suppliers', value: 'suppliers' },
    { name: 'Milk Entries', value: 'milk_entries' },
    { name: 'Transport Incentives', value: 'transport_incentives' },
    { name: 'Rate Master', value: 'rate_master' },
    { name: 'Weekly Cutoff', value: 'weekly_cutoff' },
    { name: 'Customer Master', value: 'customers' }
  ];
  
  const [selectedTables, setSelectedTables] = useState(tables.map(t => t.value));
  
  // Import functions
  const handleImportTableChange = (e) => {
    setSelectedImportTable(e.target.value);
  };
  
  const triggerFileInput = () => {
    fileInputRef.current.click();
  };
  
  const handleFileUpload = async (e) => {
    const file = e.target.files[0];
    if (!file) return;
    
    if (!selectedImportTable) {
      setImportStatus('Please select a table to import data into');
      return;
    }
    
    setIsImporting(true);
    setImportStatus('Importing data...');
    
    try {
      // Read the file
      const reader = new FileReader();
      
      reader.onload = async (e) => {
        try {
          const data = new Uint8Array(e.target.result);
          const workbook = XLSX.read(data, { type: 'array' });
          
          // Get the first sheet
          const firstSheetName = workbook.SheetNames[0];
          const worksheet = workbook.Sheets[firstSheetName];
          
          // Convert to JSON
          const jsonData = XLSX.utils.sheet_to_json(worksheet);
          
          if (jsonData.length === 0) {
            setImportStatus('No data found in the file');
            setIsImporting(false);
            return;
          }
          
          // Send to backend
          await window.electronAPI.importTableData(selectedImportTable, jsonData);
          
          setImportStatus(`Successfully imported ${jsonData.length} records into ${selectedImportTable}`);
        } catch (error) {
          console.error('Import processing error:', error);
          setImportStatus(`Import failed: ${error.message}`);
        } finally {
          setIsImporting(false);
        }
      };
      
      reader.onerror = () => {
        setImportStatus('Error reading file');
        setIsImporting(false);
      };
      
      reader.readAsArrayBuffer(file);
      
    } catch (error) {
      console.error('Import failed:', error);
      setImportStatus(`Import failed: ${error.message}`);
      setIsImporting(false);
    }
  };
  
  const handleTableSelection = (table) => {
    if (selectedTables.includes(table)) {
      setSelectedTables(selectedTables.filter(t => t !== table));
    } else {
      setSelectedTables([...selectedTables, table]);
    }
  };
  
  const handleSelectAll = () => {
    setSelectedTables(tables.map(t => t.value));
  };
  
  const handleDeselectAll = () => {
    setSelectedTables([]);
  };
  
  const exportData = async () => {
    if (selectedTables.length === 0) {
      setExportStatus('Please select at least one table to export');
      return;
    }
    
    setIsExporting(true);
    setExportStatus('Exporting data...');
    
    try {
      // Get data for all selected tables
      const data = {};
      for (const table of selectedTables) {
        const tableData = await window.electronAPI.getTableData(table);
        data[table] = tableData;
      }
      
      // Export based on selected format
      if (exportFormat === 'excel') {
        exportToExcel(data);
      } else if (exportFormat === 'csv') {
        exportToCSV(data);
      } else if (exportFormat === 'json') {
        exportToJSON(data);
      }
      
      setExportStatus('Data exported successfully!');
    } catch (error) {
      console.error('Export failed:', error);
      setExportStatus(`Export failed: ${error.message}`);
    } finally {
      setIsExporting(false);
    }
  };
  
  const exportToExcel = (data) => {
    const wb = XLSX.utils.book_new();
    
    // Create a worksheet for each table
    for (const [tableName, tableData] of Object.entries(data)) {
      if (tableData && tableData.length > 0) {
        const ws = XLSX.utils.json_to_sheet(tableData);
        XLSX.utils.book_append_sheet(wb, ws, tableName);
      }
    }
    
    // Generate filename with date
    const date = new Date().toISOString().split('T')[0];
    const fileName = `Database_Export_${date}.xlsx`;
    
    // Write and download
    XLSX.writeFile(wb, fileName);
  };
  
  const exportToCSV = (data) => {
    // Create a zip folder with CSV files
    for (const [tableName, tableData] of Object.entries(data)) {
      if (tableData && tableData.length > 0) {
        const ws = XLSX.utils.json_to_sheet(tableData);
        const csv = XLSX.utils.sheet_to_csv(ws);
        
        // Create a download link
        const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.setAttribute('href', url);
        link.setAttribute('download', `${tableName}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }
    }
  };
  
  const exportToJSON = (data) => {
    // Download as a single JSON file
    const jsonStr = JSON.stringify(data, null, 2);
    const blob = new Blob([jsonStr], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    
    const date = new Date().toISOString().split('T')[0];
    const fileName = `Database_Export_${date}.json`;
    
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', fileName);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };
  
  return (
    <div className="p-6 max-w-6xl mx-auto">
      <div className="bg-gradient-to-r from-blue-500 to-teal-400 p-4 rounded-t-lg shadow-md">
        <h1 className="text-2xl font-bold text-center text-white">DATABASE TOOLS</h1>
      </div>
      
      <div className="bg-white rounded-b-lg shadow-md p-6">
        {/* Import Section */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold mb-4">Import Data</h2>
          
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">Select Table to Import</label>
            <select
              value={selectedImportTable}
              onChange={handleImportTableChange}
              className="w-full md:w-1/2 border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">-- Select a table --</option>
              {tables.map(table => (
                <option key={`import-${table.value}`} value={table.value}>
                  {table.name}
                </option>
              ))}
            </select>
          </div>
          
          <div className="flex items-center mb-4">
            <input
              type="file"
              ref={fileInputRef}
              onChange={handleFileUpload}
              accept=".xlsx,.xls,.csv,.json"
              className="hidden"
            />
            <button
              onClick={triggerFileInput}
              disabled={isImporting || !selectedImportTable}
              className={`bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition-colors flex items-center ${
                (isImporting || !selectedImportTable) ? 'opacity-50 cursor-not-allowed' : ''
              }`}
            >
              <FiUpload className="mr-2" />
              {isImporting ? 'Importing...' : 'Select File & Import'}
            </button>
            <span className="ml-4 text-sm text-gray-500">
              Supported formats: Excel, CSV, JSON
            </span>
          </div>
          
          {importStatus && (
            <div className={`mt-2 p-3 rounded ${
              importStatus.includes('failed') ? 'bg-red-100 text-red-700' : 
              importStatus.includes('Success') ? 'bg-green-100 text-green-700' : 'bg-blue-100 text-blue-700'
            }`}>
              {importStatus}
            </div>
          )}
          
          <div className="mt-4 bg-yellow-50 border-l-4 border-yellow-400 p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <FiCheck className="h-5 w-5 text-yellow-400" />
              </div>
              <div className="ml-3">
                <p className="text-sm text-yellow-700">
                  <strong>Import Format Requirements:</strong>
                </p>
                <ul className="mt-1 text-sm text-yellow-700 list-disc list-inside">
                  <li>Column names should match database fields</li>
                  <li>For routes: code, route_name, cell_no, ac_code</li>
                  <li>For suppliers: code, name, route, cell_no, bonus_amount, milk_rate</li>
                  <li>For milk entries: date, shift, supplier_code, kgs, ltrs, fat, clr, snf, rate, amount</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
        
        <div className="border-t border-gray-200 my-6"></div>
        
        {/* Export Section */}
        <div className="mb-6">
          <h2 className="text-xl font-semibold mb-4">Export Database</h2>
          
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">Export Format</label>
            <div className="flex space-x-4">
              <label className="inline-flex items-center">
                <input
                  type="radio"
                  className="form-radio"
                  name="exportFormat"
                  value="excel"
                  checked={exportFormat === 'excel'}
                  onChange={() => setExportFormat('excel')}
                />
                <span className="ml-2">Excel (.xlsx)</span>
              </label>
              <label className="inline-flex items-center">
                <input
                  type="radio"
                  className="form-radio"
                  name="exportFormat"
                  value="csv"
                  checked={exportFormat === 'csv'}
                  onChange={() => setExportFormat('csv')}
                />
                <span className="ml-2">CSV</span>
              </label>
              <label className="inline-flex items-center">
                <input
                  type="radio"
                  className="form-radio"
                  name="exportFormat"
                  value="json"
                  checked={exportFormat === 'json'}
                  onChange={() => setExportFormat('json')}
                />
                <span className="ml-2">JSON</span>
              </label>
            </div>
          </div>
          
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">Select Tables to Export</label>
            <div className="flex mb-2">
              <button 
                onClick={handleSelectAll}
                className="bg-blue-100 text-blue-700 px-3 py-1 rounded text-sm mr-2 hover:bg-blue-200"
              >
                Select All
              </button>
              <button 
                onClick={handleDeselectAll}
                className="bg-gray-100 text-gray-700 px-3 py-1 rounded text-sm hover:bg-gray-200"
              >
                Deselect All
              </button>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
              {tables.map(table => (
                <label key={table.value} className="inline-flex items-center">
                  <input
                    type="checkbox"
                    className="form-checkbox"
                    checked={selectedTables.includes(table.value)}
                    onChange={() => handleTableSelection(table.value)}
                  />
                  <span className="ml-2">{table.name}</span>
                </label>
              ))}
            </div>
          </div>
          
          <button
            onClick={exportData}
            disabled={isExporting || selectedTables.length === 0}
            className={`bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-colors flex items-center ${
              (isExporting || selectedTables.length === 0) ? 'opacity-50 cursor-not-allowed' : ''
            }`}
          >
            <FiDownload className="mr-2" />
            {isExporting ? 'Exporting...' : 'Export Data'}
          </button>
          
          {exportStatus && (
            <div className={`mt-4 p-3 rounded ${
              exportStatus.includes('failed') ? 'bg-red-100 text-red-700' : 
              exportStatus.includes('success') ? 'bg-green-100 text-green-700' : 'bg-blue-100 text-blue-700'
            }`}>
              {exportStatus}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default Tools;
