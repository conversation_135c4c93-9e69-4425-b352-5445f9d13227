import React, { useState, useEffect } from 'react';

function RouteName() {
  const [routes, setRoutes] = useState([]);
  const [selectedRoute, setSelectedRoute] = useState(null);
  const [formData, setFormData] = useState({
    code: '',
    routeName: '',
    cellNo: '',
    acCode: ''
  });
  const [isEditing, setIsEditing] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    loadRoutes();
  }, []);

  const loadRoutes = async () => {
    if (window.electronAPI) {
      try {
        const data = await window.electronAPI.getRoutes();
        setRoutes(data || []);
      } catch (error) {
        console.error('Failed to load routes:', error);
      }
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const resetForm = () => {
    setFormData({
      code: '',
      routeName: '',
      cellNo: '',
      acCode: ''
    });
    setSelectedRoute(null);
    setIsEditing(false);
  };

  const handleAdd = async () => {
    if (!formData.routeName) {
      alert("Please enter route name");
      return;
    }

    try {
      await window.electronAPI.saveRoute(formData);
      resetForm();
      loadRoutes();
    } catch (error) {
      console.error('Failed to save route:', error);
      alert("Failed to save route: " + error.message);
    }
  };

  const handleEdit = (route) => {
    setFormData({
      id: route.id,
      code: route.code || '',
      routeName: route.route_name || '',
      cellNo: route.cell_no || '',
      acCode: route.ac_code || ''
    });
    setSelectedRoute(route);
    setIsEditing(true);
  };

  const handleUpdate = async () => {
    if (!formData.routeName) {
      alert("Please enter route name");
      return;
    }

    try {
      await window.electronAPI.saveRoute(formData);
      resetForm();
      loadRoutes();
    } catch (error) {
      console.error('Failed to update route:', error);
      alert("Failed to update route: " + error.message);
    }
  };

  const handleDelete = async () => {
    if (!selectedRoute) return;
    
    if (window.confirm("Are you sure you want to delete this route?")) {
      try {
        await window.electronAPI.deleteRoute(selectedRoute.id);
        resetForm();
        loadRoutes();
      } catch (error) {
        console.error('Failed to delete route:', error);
        alert("Failed to delete route: " + error.message);
      }
    }
  };

  const filteredRoutes = routes.filter(route => 
    route.code?.toLowerCase().includes(searchTerm.toLowerCase()) || 
    route.route_name?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="p-6 max-w-6xl mx-auto">
      <div className="bg-gradient-to-r from-blue-500 to-teal-400 p-4 rounded-t-lg shadow-md">
        <h1 className="text-2xl font-bold text-center text-white">ROUTE NAME ENTRY</h1>
      </div>
      
      <div className="bg-white rounded-b-lg shadow-md overflow-hidden">
        {/* Form Section */}
        <div className="p-6 border-b border-gray-200">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="relative">
              <label className="block text-sm font-medium text-gray-700 mb-1">Code</label>
              <input
                type="text"
                name="code"
                value={formData.code}
                onChange={handleInputChange}
                className="w-full border border-gray-300 px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                placeholder="Enter code"
              />
            </div>
            
            <div className="relative">
              <label className="block text-sm font-medium text-gray-700 mb-1">Route Name</label>
              <input
                type="text"
                name="routeName"
                value={formData.routeName}
                onChange={handleInputChange}
                className="w-full border border-gray-300 px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                placeholder="Enter route name"
              />
            </div>
            
            <div className="relative">
              <label className="block text-sm font-medium text-gray-700 mb-1">Cell No.</label>
              <input
                type="text"
                name="cellNo"
                value={formData.cellNo}
                onChange={handleInputChange}
                className="w-full border border-gray-300 px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                placeholder="Enter cell number"
              />
            </div>
            
            <div className="relative">
              <label className="block text-sm font-medium text-gray-700 mb-1">A/c Code</label>
              <input
                type="text"
                name="acCode"
                value={formData.acCode}
                onChange={handleInputChange}
                className="w-full border border-gray-300 px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                placeholder="Enter account code"
              />
            </div>
          </div>
          
          <div className="flex justify-center mt-6 space-x-3">
            {isEditing ? (
              <>
                <button 
                  onClick={handleUpdate}
                  className="bg-blue-600 text-white px-5 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors flex items-center"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                  </svg>
                  Update
                </button>
                <button 
                  onClick={handleDelete}
                  className="bg-red-600 text-white px-5 py-2 rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-colors flex items-center"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                  Delete
                </button>
              </>
            ) : (
              <button 
                onClick={handleAdd}
                className="bg-green-600 text-white px-5 py-2 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-colors flex items-center"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
                </svg>
                Add
              </button>
            )}
            <button 
              onClick={resetForm}
              className="bg-gray-600 text-white px-5 py-2 rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors flex items-center"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
              Cancel
            </button>
          </div>
        </div>
        
        {/* Table Section */}
        <div className="p-6">
          <div className="mb-4">
            <div className="relative">
              <input
                type="text"
                placeholder="Search routes..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full border border-gray-300 pl-10 pr-4 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
              <div className="absolute left-3 top-2.5 text-gray-400">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd" />
                </svg>
              </div>
            </div>
          </div>
          
          <div className="overflow-x-auto rounded-lg border border-gray-200">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Route Name</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Cell No.</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">A/c Code</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredRoutes.length > 0 ? (
                  filteredRoutes.map((route) => (
                    <tr 
                      key={route.id} 
                      className={`cursor-pointer hover:bg-blue-50 transition-colors ${selectedRoute?.id === route.id ? 'bg-blue-100' : ''}`}
                      onClick={() => handleEdit(route)}
                    >
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{route.code}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{route.route_name}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{route.cell_no}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{route.ac_code}</td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan="4" className="px-6 py-4 text-center text-sm text-gray-500">
                      {searchTerm ? "No routes found matching your search" : "No routes available"}
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
          
          <div className="mt-4 text-right text-sm text-gray-500">
            Total Routes: {filteredRoutes.length}
          </div>
        </div>
      </div>
    </div>
  );
}

export default RouteName;
